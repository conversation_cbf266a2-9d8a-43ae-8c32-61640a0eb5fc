/* Modern Dark Portfolio CSS */

/* CSS Variables */
:root {
  --primary-color: #00d4ff;
  --secondary-color: #ff6b6b;
  --accent-color: #4ecdc4;
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #2a2a2a;
  --text-primary: #ffffff;
  --text-secondary: #b0b0b0;
  --text-muted: #666666;
  --gradient-primary: linear-gradient(135deg, #00d4ff 0%, #4ecdc4 100%);
  --gradient-secondary: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
  --shadow-light: 0 4px 20px rgba(0, 212, 255, 0.1);
  --shadow-medium: 0 8px 40px rgba(0, 212, 255, 0.2);
  --shadow-heavy: 0 20px 60px rgba(0, 0, 0, 0.3);
  --border-radius: 12px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
}

/* Orbital Preloader Styles */
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity 0.8s ease, visibility 0.8s ease;
  overflow: hidden;
}

.preloader.hidden {
  opacity: 0;
  visibility: hidden;
}

.preloader-content {
  text-align: center;
  position: relative;
  z-index: 2;
}

/* Skip Button */
.preloader-skip {
  position: absolute;
  top: 2rem;
  right: 2rem;
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  font-family: 'JetBrains Mono', monospace;
}

.preloader-skip:hover {
  background: rgba(0, 212, 255, 0.2);
  transform: translateY(-2px);
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .preloader {
    transition: opacity 0.3s ease, visibility 0.3s ease;
  }

  .orbit {
    animation: none !important;
  }

  .skill-icon {
    animation: none !important;
    opacity: 1 !important;
    transform: none !important;
  }

  .name-decoder {
    animation: none !important;
  }

  .loading-progress-container {
    animation: none !important;
  }
}

/* Orbital System with 3D Perspective */
.orbit-container {
  position: relative;
  width: 400px;
  height: 400px;
  margin: 0 auto 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 1000px;
  perspective-origin: center center;
}

/* Name Decoding Animation - Centered in Orbit */
.name-decoder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: clamp(1.2rem, 3vw, 1.8rem);
  font-weight: 700;
  font-family: 'JetBrains Mono', 'Fira Code', 'Source Code Pro', 'Courier New', monospace;
  z-index: 10;
  text-align: center;
  white-space: nowrap;
}

.decoded-name {
  color: var(--primary-color);
  letter-spacing: 0.15em;
  position: relative;
  z-index: 2;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.decoding-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'JetBrains Mono', 'Fira Code', 'Source Code Pro', 'Courier New', monospace;
  color: var(--primary-color);
  letter-spacing: 0.15em;
  z-index: 3;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.orbit {
  position: absolute;
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.1);
  opacity: 0;
  will-change: transform, opacity;
  backface-visibility: hidden;
}

.orbit-inner {
  width: 120px;
  height: 120px;
  animation: orbitRotateClockwise 8s linear infinite;
}

.orbit-middle {
  width: 200px;
  height: 200px;
  animation: orbitRotateClockwise 12s linear infinite;
}

.orbit-outer {
  width: 280px;
  height: 280px;
  animation: orbitRotateClockwise 16s linear infinite;
}

.skill-icon {
  position: absolute;
  width: 40px;
  height: 40px;
  background: rgba(0, 212, 255, 0.1);
  border: 2px solid var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 1.2rem;
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  animation: skillIconFloat 2s ease-in-out infinite;
  transform-style: preserve-3d;
  opacity: 0;
  transform: scale(0) translateZ(0);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .orbit-container {
    width: 300px;
    height: 300px;
    perspective: 500px;
  }

  .orbit-inner {
    width: 80px;
    height: 80px;
    animation-duration: 8s;
  }

  .orbit-middle {
    width: 140px;
    height: 140px;
    animation-duration: 10s;
  }

  .orbit-outer {
    width: 200px;
    height: 200px;
    animation-duration: 12s;
  }

  .skill-icon {
    width: 30px;
    height: 30px;
    font-size: 1rem;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
  }

  .name-decoder {
    font-size: clamp(1rem, 4vw, 1.4rem);
  }

  .loading-progress-container {
    width: 280px;
  }

  .preloader-skip {
    top: 1rem;
    right: 1rem;
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .orbit {
    border-color: #ffffff;
  }

  .skill-icon {
    border-color: #ffffff;
    color: #ffffff;
  }

  .loading-text,
  .loading-percentage {
    color: #ffffff;
  }
}

/* Error state styles */
.preloader.error {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d1b1b 100%);
}

.preloader.error .loading-text {
  color: #ff6b6b;
}

.preloader.error .progress-fill {
  background: linear-gradient(90deg, #ff6b6b, #ff8e8e);
}

/* Performance optimizations */
.orbit,
.skill-icon,
.progress-fill,
.name-decoder {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Position skill icons around orbits */
.orbit-inner .skill-icon:nth-child(1) { top: -20px; left: calc(50% - 20px); animation-delay: 0s; }
.orbit-inner .skill-icon:nth-child(2) { right: -20px; top: calc(50% - 20px); animation-delay: 0.5s; }
.orbit-inner .skill-icon:nth-child(3) { bottom: -20px; left: calc(50% - 20px); animation-delay: 1s; }
.orbit-inner .skill-icon:nth-child(4) { left: -20px; top: calc(50% - 20px); animation-delay: 1.5s; }

.orbit-middle .skill-icon:nth-child(1) { top: -20px; left: calc(50% - 20px); animation-delay: 0.2s; }
.orbit-middle .skill-icon:nth-child(2) { top: calc(15% - 20px); right: calc(15% - 20px); animation-delay: 0.7s; }
.orbit-middle .skill-icon:nth-child(3) { right: -20px; top: calc(50% - 20px); animation-delay: 1.2s; }
.orbit-middle .skill-icon:nth-child(4) { bottom: calc(15% - 20px); right: calc(15% - 20px); animation-delay: 1.7s; }
.orbit-middle .skill-icon:nth-child(5) { bottom: -20px; left: calc(50% - 20px); animation-delay: 2.2s; }

.orbit-outer .skill-icon:nth-child(1) { top: -20px; left: calc(50% - 20px); animation-delay: 0.3s; }
.orbit-outer .skill-icon:nth-child(2) { top: calc(10% - 20px); right: calc(25% - 20px); animation-delay: 0.8s; }
.orbit-outer .skill-icon:nth-child(3) { right: -20px; top: calc(30% - 20px); animation-delay: 1.3s; }
.orbit-outer .skill-icon:nth-child(4) { right: -20px; bottom: calc(30% - 20px); animation-delay: 1.8s; }
.orbit-outer .skill-icon:nth-child(5) { bottom: calc(10% - 20px); right: calc(25% - 20px); animation-delay: 2.3s; }
.orbit-outer .skill-icon:nth-child(6) { bottom: -20px; left: calc(50% - 20px); animation-delay: 2.8s; }

/* Loading Progress */
.loading-progress-container {
  width: 400px;
  margin: 0 auto;
}

.loading-text {
  color: var(--primary-color);
  font-size: 0.9rem;
  letter-spacing: 2px;
  margin-bottom: 1rem;
  font-family: 'Courier New', monospace;
  will-change: contents;
}

.progress-bar {
  width: 100%;
  height: 3px;
  background: rgba(0, 212, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), #4ecdcc);
  border-radius: 2px;
  width: 0%;
  box-shadow: 0 0 10px var(--primary-color);
  will-change: width;
  transform: translateZ(0);
}

.loading-percentage {
  color: var(--text-secondary);
  font-size: 0.8rem;
  font-family: 'Courier New', monospace;
  will-change: contents;
}

/* Loading Status Indicator */
.loading-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.loading-status .status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--primary-color);
  animation: statusPulse 1.5s ease-in-out infinite;
}

@keyframes statusPulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

/* Background Effects */
.preloader-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  opacity: 0.3;
}

.scan-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    0deg,
    transparent,
    transparent 2px,
    rgba(0, 212, 255, 0.03) 2px,
    rgba(0, 212, 255, 0.03) 4px
  );
  pointer-events: none;
}

/* Animations */
@keyframes orbitRotateClockwise {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes skillIconFloat {
  0%, 100% {
    transform: translateY(0px) scale(1) rotateX(0deg);
  }
  50% {
    transform: translateY(-5px) scale(1.1) rotateX(180deg);
  }
}

@keyframes loadingProgress {
  0% { width: 0%; }
  100% { width: 100%; }
}

/* 3D Orbit Approach Animation */
@keyframes orbitApproach {
  0% {
    transform: translate(-50%, -50%) translateZ(-500px) scale(0.1);
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    transform: translate(-50%, -50%) translateZ(0) scale(1);
    opacity: 1;
  }
}

/* Navigation Styles */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(10, 10, 10, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 1000;
  transition: var(--transition);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-ac {
  font-size: 1.5rem;
  font-weight: 800;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: 'JetBrains Mono', monospace;
}

.logo-name {
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: 500;
}

.nav-menu {
  display: flex;
  gap: 2rem;
  list-style: none;
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  position: relative;
  transition: var(--transition);
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  transition: var(--transition);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--text-primary);
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.btn-secondary {
  background: transparent;
  color: var(--text-primary);
  padding: 0.75rem 1.5rem;
  border: 2px solid var(--primary-color);
  border-radius: var(--border-radius);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-secondary:hover {
  background: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.hamburger span {
  width: 25px;
  height: 3px;
  background: var(--text-primary);
  transition: var(--transition);
}

/* Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at center, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
}

/* Mouse Trail Particles */
.particle-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

/* Wave Distortion Effect */
.wave-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.wave-svg {
  position: absolute;
  bottom: 200px;
  left: 0;
  width: 100%;
  height: 100%;
  transform: translateY(50%);
}

.wave {
  transform-origin: center;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-text {
  z-index: 2;
}

.hero-greeting {
  margin-bottom: 1rem;
}

.greeting-text {
  font-size: 1.2rem;
  color: var(--text-secondary);
  font-weight: 400;
}

.hero-name {
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 800;
  line-height: 0.9;
  margin-bottom: 1rem;
}

.name-first {
  display: block;
  color: var(--text-primary);
}

.name-last {
  display: block;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Gradient Text Animation */
.gradient-text {
  position: relative;
  background: linear-gradient(
    45deg,
    #00d4ff 0%,
    #4ecdcc 25%,
    #00d4ff 50%,
    #4ecdcc 75%,
    #00d4ff 100%
  );
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  opacity: 0;
  transform: scale(0);
}

.name-first.gradient-text {
  color: var(--text-primary);
  background: linear-gradient(
    45deg,
    var(--text-primary) 0%,
    #00d4ff 25%,
    var(--text-primary) 50%,
    #00d4ff 75%,
    var(--text-primary) 100%
  );
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Gradient flow animation */
@keyframes gradientFlow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Cyberpunk Glitch Effect */
.glitch-effect {
  position: relative;
}

.glitch-effect::before,
.glitch-effect::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: inherit;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  opacity: 0;
  pointer-events: none;
}

.glitch-effect::before {
  color: #ff0040;
  z-index: -1;
  /* animation: glitch-1 0.3s ease-in-out; */ /* Disabled - only animate when .active */
}

.glitch-effect::after {
  color: #00ffff;
  z-index: -2;
  /* animation: glitch-2 0.3s ease-in-out; */ /* Disabled - only animate when .active */
}

.glitch-effect.active::before {
  opacity: 0.8;
  animation: glitch-1 0.3s ease-in-out;
}

.glitch-effect.active::after {
  opacity: 0.8;
  animation: glitch-2 0.3s ease-in-out;
}

@keyframes glitch-1 {
  0%, 100% {
    transform: translate(0);
    opacity: 0;
  }
  20% {
    transform: translate(-2px, 2px);
    opacity: 0.8;
  }
  40% {
    transform: translate(-2px, -2px);
    opacity: 0.8;
  }
  60% {
    transform: translate(2px, 2px);
    opacity: 0.8;
  }
  80% {
    transform: translate(2px, -2px);
    opacity: 0.8;
  }
}

@keyframes glitch-2 {
  0%, 100% {
    transform: translate(0);
    opacity: 0;
  }
  20% {
    transform: translate(2px, -2px);
    opacity: 0.8;
  }
  40% {
    transform: translate(2px, 2px);
    opacity: 0.8;
  }
  60% {
    transform: translate(-2px, -2px);
    opacity: 0.8;
  }
  80% {
    transform: translate(-2px, 2px);
    opacity: 0.8;
  }
}

/* Profile image glitch effect */
.profile-glitch {
  position: relative;
  overflow: visible;
}

.profile-glitch::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: inherit;
  border-radius: inherit;
  opacity: 0;
  z-index: -1;
  filter: hue-rotate(90deg) saturate(2);
  transform: translate(0);
}

.profile-glitch::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: inherit;
  border-radius: inherit;
  opacity: 0;
  z-index: -2;
  filter: hue-rotate(180deg) saturate(2);
  transform: translate(0);
}

.profile-glitch.active::before {
  opacity: 0.6;
  animation: profile-glitch-1 0.3s ease-in-out;
}

.profile-glitch.active::after {
  opacity: 0.6;
  animation: profile-glitch-2 0.3s ease-in-out;
}

@keyframes profile-glitch-1 {
  0%, 100% {
    transform: translate(0);
    opacity: 0;
  }
  25% {
    transform: translate(-3px, 3px);
    opacity: 0.6;
  }
  50% {
    transform: translate(3px, -3px);
    opacity: 0.6;
  }
  75% {
    transform: translate(-2px, -2px);
    opacity: 0.6;
  }
}

@keyframes profile-glitch-2 {
  0%, 100% {
    transform: translate(0);
    opacity: 0;
  }
  25% {
    transform: translate(3px, -3px);
    opacity: 0.6;
  }
  50% {
    transform: translate(-3px, 3px);
    opacity: 0.6;
  }
  75% {
    transform: translate(2px, 2px);
    opacity: 0.6;
  }
}

.hero-title {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.title-prefix {
  color: var(--text-secondary);
}

.title-dynamic {
  color: var(--primary-color);
  font-weight: 600;
  min-width: 200px;
}

.cursor {
  color: var(--primary-color);
  animation: blink 1s infinite;
}

.hero-description {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: 2.5rem;
  max-width: 500px;
  line-height: 1.7;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.hero-social {
  display: flex;
  gap: 1rem;
}

.social-link {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-link:hover {
  background: var(--primary-color);
  color: var(--text-primary);
  transform: translateY(-3px);
  box-shadow: var(--shadow-light);
}

/* Hero Visual */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.profile-container {
  position: relative;
  width: 400px;
  height: 400px;
}

.profile-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  border: 4px solid var(--primary-color);
  box-shadow: var(--shadow-heavy);
}

.profile-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-glow {
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  border-radius: 50%;
  background: var(--gradient-primary);
  opacity: 0.3;
  filter: blur(20px);
  z-index: -1;
  animation: pulse 3s ease-in-out infinite;
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.floating-icon {
  position: absolute;
  width: 60px;
  height: 60px;
  background: var(--bg-secondary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 1.5rem;
  border: 2px solid var(--primary-color);
  animation: float 6s ease-in-out infinite;
}

.floating-icon[data-icon="brain"] {
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.floating-icon[data-icon="code"] {
  top: 60%;
  right: -10%;
  animation-delay: 1.5s;
}

.floating-icon[data-icon="robot"] {
  bottom: 10%;
  left: 10%;
  animation-delay: 3s;
}

.floating-icon[data-icon="palette"] {
  top: 30%;
  left: -10%;
  animation-delay: 4.5s;
}

/* Scroll Indicator */
.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: var(--text-secondary);
  cursor: pointer;
}

.scroll-text {
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  letter-spacing: 1px;
}

.scroll-arrow {
  animation: bounce 2s infinite;
}

/* Animations */
@keyframes loadingProgress {
  0% { width: 0%; }
  100% { width: 100%; }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.5; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-10px) rotate(5deg); }
  50% { transform: translateY(-20px) rotate(0deg); }
  75% { transform: translateY(-10px) rotate(-5deg); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }
  
  .hamburger {
    display: flex;
  }
  
  .hero-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .profile-container {
    width: 300px;
    height: 300px;
  }
  
  .hero-actions {
    justify-content: center;
  }
  
  .hero-social {
    justify-content: center;
  }
  
  .floating-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: 1rem;
  }

  .hero-content {
    padding: 1rem;
  }

  .profile-container {
    width: 250px;
    height: 250px;
  }

  .btn-primary,
  .btn-secondary {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
}

/* Section Container Styles */
.section-container {
  min-height: 100vh;
  padding: 5rem 2rem;
  position: relative;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

.section-content {
  max-width: 1400px;
  margin: 0 auto;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary);
  font-weight: 300;
  letter-spacing: 1px;
}

/* Cards Grid */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

/* Neon Card Styles */
.neon-card {
  background: rgba(26, 26, 26, 0.8);
  border-radius: 20px;
  border: 1px solid rgba(0, 212, 255, 0.3);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
  transition: var(--transition);
  height: 800px;
  display: flex;
  flex-direction: column;
}

/* Traveling light effect around border path only */
.neon-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 22px;
  background: conic-gradient(
    from 0deg,
    transparent 0deg,
    transparent 85%,
    rgba(0, 212, 255, 0.3) 88%,
    rgba(0, 212, 255, 1) 92%,
    rgba(78, 205, 196, 1) 95%,
    rgba(0, 212, 255, 1) 98%,
    rgba(0, 212, 255, 0.3) 100%,
    transparent 0deg
  );
  opacity: 0;
  z-index: -1;
  animation: travelBorder 2.5s linear infinite;
  animation-play-state: paused;
  -webkit-mask:
    linear-gradient(#000 0 0) padding-box,
    linear-gradient(#000 0 0);
  -webkit-mask-composite: xor;
  mask:
    linear-gradient(#000 0 0) padding-box,
    linear-gradient(#000 0 0);
  mask-composite: exclude;
  padding: 2px;
}

.neon-card.card-hover::before {
  opacity: 1;
  animation-play-state: running;
}

/* Background overlay */
.neon-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(0, 212, 255, 0.1) 0%,
    rgba(78, 205, 196, 0.1) 50%,
    rgba(255, 107, 107, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 1;
}

.neon-card.card-hover::after {
  opacity: 1;
}

/* Enhanced neon glow on hover */
.neon-card.card-hover {
  border-color: rgba(0, 212, 255, 0.8);
  box-shadow:
    0 0 20px rgba(0, 212, 255, 0.3),
    0 0 40px rgba(0, 212, 255, 0.2),
    0 0 60px rgba(78, 205, 196, 0.1),
    inset 0 0 20px rgba(0, 212, 255, 0.1);
}

/* Card Header */
.card-header {
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 2;
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: var(--text-primary);
  box-shadow: var(--shadow-light);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.card-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    rgba(255, 255, 255, 0.2),
    transparent,
    rgba(255, 255, 255, 0.1));
  opacity: 0;
  transition: opacity 0.4s ease;
}

.neon-card.card-hover .card-icon {
  border-radius: 50%;
  transform: scale(1.1) rotate(360deg);
  box-shadow:
    0 0 20px rgba(0, 212, 255, 0.4),
    0 0 40px rgba(0, 212, 255, 0.2),
    var(--shadow-light);
}

.neon-card.card-hover .card-icon::before {
  opacity: 1;
}

.card-icon i {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 2;
  position: relative;
}

.neon-card.card-hover .card-icon i {
  transform: scale(1.1);
}

.card-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* Card Content */
.card-content {
  flex: 1;
  position: relative;
  z-index: 2;
  overflow: hidden;
}

.scrollable-content {
  height: 100%;
  padding: 1.5rem;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) transparent;
}

.scrollable-content::-webkit-scrollbar {
  width: 6px;
}

.scrollable-content::-webkit-scrollbar-track {
  background: transparent;
}

.scrollable-content::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: 3px;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

/* Personal Info Card Styles */
.info-grid {
  display: grid;
  gap: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 3px solid var(--primary-color);
}

.info-label {
  font-weight: 500;
  color: var(--text-secondary);
}

.info-value {
  font-weight: 600;
  color: var(--text-primary);
}

.status-available {
  color: var(--accent-color) !important;
  font-weight: 700;
}

/* Journey Card Styles */
.journey-text {
  margin-bottom: 1.5rem;
  line-height: 1.7;
  color: var(--text-secondary);
}

.journey-text strong {
  color: var(--primary-color);
  font-weight: 600;
}

/* Skills Card Styles */
.skills-category {
  margin-bottom: 2rem;
}

.category-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-color);
}

.skill-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.skill-tag {
  background: rgba(0, 212, 255, 0.1);
  color: var(--primary-color);
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid rgba(0, 212, 255, 0.3);
  transition: var(--transition);
}

.skill-tag:hover {
  background: var(--primary-color);
  color: var(--text-primary);
  transform: translateY(-2px);
}

/* Language Skills */
.language-skills {
  display: grid;
  gap: 1rem;
}

.language-item {
  display: grid;
  grid-template-columns: 80px 1fr 80px;
  align-items: center;
  gap: 1rem;
}

.language-name {
  font-weight: 600;
  color: var(--text-primary);
}

.language-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.language-progress {
  height: 100%;
  background: var(--gradient-primary);
  border-radius: 4px;
  transition: width 1s ease;
}

.language-level {
  font-size: 0.85rem;
  color: var(--text-secondary);
  text-align: right;
}

/* Education Timeline */
.education-timeline {
  position: relative;
  padding-left: 2rem;
}

.education-timeline::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--gradient-primary);
}

.timeline-item {
  position: relative;
  margin-bottom: 2rem;
  padding-left: 1rem;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -1.5rem;
  top: 0.5rem;
  width: 12px;
  height: 12px;
  background: var(--primary-color);
  border-radius: 50%;
  border: 3px solid var(--bg-secondary);
}

.timeline-date {
  font-size: 0.9rem;
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.timeline-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.timeline-institution {
  font-size: 0.95rem;
  color: var(--accent-color);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.timeline-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* Traveling Border Light Animation */
@keyframes travelBorder {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Card Responsive Design */
@media (max-width: 768px) {
  .cards-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .neon-card {
    height: 700px;
  }

  .card-header {
    padding: 1rem;
  }

  .scrollable-content {
    padding: 1rem;
  }

  .language-item {
    grid-template-columns: 70px 1fr 70px;
    gap: 0.5rem;
  }

  .education-timeline {
    padding-left: 1.5rem;
  }
}

@media (max-width: 480px) {
  .section-container {
    padding: 3rem 1rem;
  }

  .cards-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .neon-card {
    height: 640px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .skill-tags {
    gap: 0.25rem;
  }

  .skill-tag {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
  }
}

/* Portfolio Section Styles */
.portfolio-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem 0;
}

.portfolio-section-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.portfolio-section-title i {
  color: var(--primary-color);
  font-size: 1.5rem;
}

/* Featured Videos */
.featured-videos {
  margin-bottom: 4rem;
}

.videos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.video-card {
  background: rgba(26, 26, 26, 0.8);
  border-radius: 20px;
  border: 1px solid rgba(0, 212, 255, 0.3);
  backdrop-filter: blur(20px);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
}

.video-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: conic-gradient(
    from 0deg,
    transparent 0deg,
    transparent 85%,
    rgba(0, 212, 255, 0.3) 88%,
    rgba(0, 212, 255, 1) 92%,
    rgba(78, 205, 196, 1) 95%,
    rgba(0, 212, 255, 1) 98%,
    rgba(0, 212, 255, 0.3) 100%,
    transparent 0deg
  );
  opacity: 0;
  z-index: -1;
  animation: travelBorder 2.5s linear infinite;
  animation-play-state: paused;
  -webkit-mask:
    linear-gradient(#000 0 0) padding-box,
    linear-gradient(#000 0 0);
  -webkit-mask-composite: xor;
  mask:
    linear-gradient(#000 0 0) padding-box,
    linear-gradient(#000 0 0);
  mask-composite: exclude;
  padding: 2px;
  border-radius: 22px;
}

.video-card:hover::before {
  opacity: 1;
  animation-play-state: running;
}

.video-card:hover {
  transform: translateY(-10px);
  border-color: rgba(0, 212, 255, 0.6);
  box-shadow:
    0 20px 40px rgba(0, 212, 255, 0.2),
    0 0 60px rgba(0, 212, 255, 0.1);
}

.video-container {
  position: relative;
}

.video-placeholder {
  position: relative;
  width: 100%;
  height: 200px;
  background: linear-gradient(135deg,
    rgba(0, 212, 255, 0.1) 0%,
    rgba(78, 205, 196, 0.1) 50%,
    rgba(255, 107, 107, 0.1) 100%);
  overflow: hidden;
}

.video-thumbnail {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: rgba(0, 0, 0, 0.3);
}

.play-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: var(--text-primary);
  box-shadow:
    0 10px 30px rgba(0, 212, 255, 0.3),
    0 0 50px rgba(0, 212, 255, 0.2);
  transition: all 0.4s ease;
  z-index: 2;
}

.video-card:hover .play-button {
  transform: scale(1.1);
  box-shadow:
    0 15px 40px rgba(0, 212, 255, 0.4),
    0 0 70px rgba(0, 212, 255, 0.3);
}

.video-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 2rem 1.5rem 1.5rem;
  color: var(--text-primary);
}

.video-overlay h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.video-overlay p {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin: 0;
}

/* Video Info */
.video-info {
  padding: 1.5rem;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.video-category {
  background: rgba(0, 212, 255, 0.1);
  color: var(--primary-color);
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid rgba(0, 212, 255, 0.3);
}

.video-duration {
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
}

.video-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.video-details {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* Portfolio Links */
.portfolio-links {
  margin-top: 3rem;
}

.links-container {
  display: grid;
  gap: 2rem;
}

.main-portfolio {
  grid-column: 1 / -1;
}

.additional-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.portfolio-link-card {
  background: rgba(26, 26, 26, 0.8);
  border-radius: 20px;
  border: 1px solid rgba(0, 212, 255, 0.3);
  backdrop-filter: blur(20px);
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.portfolio-link-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(0, 212, 255, 0.05) 0%,
    rgba(78, 205, 196, 0.05) 50%,
    rgba(255, 107, 107, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 1;
}

.portfolio-link-card:hover::before {
  opacity: 1;
}

.portfolio-link-card:hover {
  transform: translateY(-10px);
  border-color: rgba(0, 212, 255, 0.6);
  box-shadow:
    0 20px 40px rgba(0, 212, 255, 0.2),
    0 0 60px rgba(0, 212, 255, 0.1);
}

.link-icon {
  width: 60px;
  height: 60px;
  border-radius: 15px;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--text-primary);
  box-shadow:
    0 10px 30px rgba(0, 212, 255, 0.3),
    0 0 50px rgba(0, 212, 255, 0.1);
  transition: all 0.4s ease;
  position: relative;
  z-index: 2;
}

.portfolio-link-card:hover .link-icon {
  transform: scale(1.1) rotate(5deg);
}

.link-content {
  flex: 1;
  position: relative;
  z-index: 2;
}

.link-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.link-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.link-stats {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--accent-color);
  font-size: 0.9rem;
  font-weight: 500;
}

.stat-item i {
  color: var(--primary-color);
}

.link-action {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-color);
  font-weight: 600;
  position: relative;
  z-index: 2;
}

.action-text {
  font-size: 1rem;
}

.link-action i {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.portfolio-link-card:hover .link-action i {
  transform: translateX(5px);
}

/* Main Portfolio Special Styling */
.main-portfolio {
  background: linear-gradient(135deg,
    rgba(26, 26, 26, 0.9) 0%,
    rgba(0, 212, 255, 0.05) 100%);
  border: 2px solid rgba(0, 212, 255, 0.4);
}

.main-portfolio .link-icon {
  width: 80px;
  height: 80px;
  font-size: 2rem;
}

.main-portfolio .link-title {
  font-size: 1.5rem;
}

.main-portfolio:hover {
  border-color: rgba(0, 212, 255, 0.8);
  box-shadow:
    0 25px 50px rgba(0, 212, 255, 0.3),
    0 0 80px rgba(0, 212, 255, 0.2);
}

/* Video Modal */
.video-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease;
}

.video-modal.active {
  opacity: 1;
  visibility: visible;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
}

.modal-content {
  position: relative;
  background: rgba(26, 26, 26, 0.95);
  border-radius: 20px;
  border: 2px solid rgba(0, 212, 255, 0.3);
  backdrop-filter: blur(30px);
  max-width: 90vw;
  max-height: 90vh;
  width: 800px;
  overflow: hidden;
  transform: scale(0.8);
  transition: transform 0.4s ease;
}

.video-modal.active .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.modal-close {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  color: #ff6b6b;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: rgba(255, 107, 107, 0.2);
  border-color: rgba(255, 107, 107, 0.6);
  transform: scale(1.1);
}

.modal-body {
  padding: 2rem;
}

.video-player-container {
  position: relative;
  width: 100%;
  height: 400px;
  background: #000;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.video-player {
  width: 100%;
  height: 100%;
  border: none;
}

.video-modal-info h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.video-modal-info p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* Portfolio Responsive Design */
@media (max-width: 768px) {
  .videos-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .additional-links {
    grid-template-columns: 1fr;
  }

  .portfolio-link-card {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .link-stats {
    justify-content: center;
  }

  .modal-content {
    width: 95vw;
    margin: 1rem;
  }

  .video-player-container {
    height: 250px;
  }
}

@media (max-width: 480px) {
  .portfolio-container {
    padding: 1rem 0;
  }

  .portfolio-section-title {
    font-size: 1.5rem;
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .video-card {
    margin: 0 0.5rem;
  }

  .portfolio-link-card {
    padding: 1.5rem;
  }

  .modal-header {
    padding: 1rem 1.5rem;
  }

  .modal-body {
    padding: 1.5rem;
  }

  .video-player-container {
    height: 200px;
  }
}

/* Terminal Contact Section */
.terminal-container {
  max-width: 1000px;
  margin: 0 auto;
  position: relative;
  perspective: 1000px;
}

.terminal-window {
  background: #0a0a0a;
  border-radius: 12px;
  border: 2px solid rgba(0, 212, 255, 0.3);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.8),
    0 0 100px rgba(0, 212, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  overflow: hidden;
  position: relative;
  transform: scale(0.1);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
}

.terminal-window:hover {
  transform: scale(1.02);
  border-color: rgba(0, 212, 255, 0.6);
  box-shadow:
    0 30px 80px rgba(0, 0, 0, 0.9),
    0 0 120px rgba(0, 212, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Terminal Header */
.terminal-header {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.terminal-controls {
  display: flex;
  gap: 0.5rem;
}

.control-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-button.close {
  background: #ff5f57;
  box-shadow: 0 0 10px rgba(255, 95, 87, 0.5);
}

.control-button.minimize {
  background: #ffbd2e;
  box-shadow: 0 0 10px rgba(255, 189, 46, 0.5);
}

.control-button.maximize {
  background: #28ca42;
  box-shadow: 0 0 10px rgba(40, 202, 66, 0.5);
}

.control-button:hover {
  transform: scale(1.2);
  filter: brightness(1.2);
}

.terminal-title {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: var(--text-primary);
  font-weight: 600;
  text-align: center;
  flex: 1;
}

.terminal-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--accent-color);
  box-shadow: 0 0 10px var(--accent-color);
  animation: pulse 2s infinite;
}

.status-text {
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  color: var(--accent-color);
  font-weight: 600;
}

/* Terminal Body */
.terminal-body {
  background: #000;
  padding: 1.5rem;
  min-height: 500px;
  font-family: 'Courier New', monospace;
  position: relative;
  overflow: hidden;
}

.terminal-output {
  margin-bottom: 2rem;
}

.output-line {
  margin-bottom: 0.5rem;
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  opacity: 0;
}

.output-line .message,
.output-line .command {
  overflow: hidden;
  white-space: nowrap;
}

.prompt {
  color: var(--primary-color);
  font-weight: 600;
  white-space: nowrap;
}

.command {
  color: var(--accent-color);
  font-weight: 500;
}

.timestamp {
  color: #666;
  font-size: 0.85rem;
  white-space: nowrap;
}

.message {
  color: var(--text-secondary);
  flex: 1;
}

.system-message .message {
  color: #888;
}

.success-message .message {
  color: var(--accent-color);
  font-weight: 500;
}

/* Terminal Form */
.terminal-form {
  margin-bottom: 2rem;
  opacity: 0;
}

.form-group {
  margin-bottom: 1rem;
}

.input-line {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.input-line.message-input {
  align-items: flex-start;
}

.input-prompt {
  color: var(--primary-color);
  font-weight: 600;
  white-space: nowrap;
  font-family: 'Courier New', monospace;
  margin-top: 0.5rem;
}

.terminal-form input,
.terminal-form textarea {
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-family: 'Courier New', monospace;
  font-size: 0.95rem;
  outline: none;
  flex: 1;
  padding: 0.5rem;
  border-bottom: 1px solid rgba(0, 212, 255, 0.3);
  transition: all 0.3s ease;
}

.terminal-form input:focus,
.terminal-form textarea:focus {
  border-bottom-color: var(--primary-color);
  box-shadow: 0 2px 10px rgba(0, 212, 255, 0.2);
}

.terminal-form textarea {
  min-height: 100px;
  resize: vertical;
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 4px;
  padding: 0.75rem;
}

.terminal-form textarea:focus {
  border-color: var(--primary-color);
}

.terminal-form input::placeholder,
.terminal-form textarea::placeholder {
  color: #666;
  font-style: italic;
}

/* Terminal Buttons */
.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  justify-content: center;
}

.terminal-button {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(78, 205, 196, 0.1) 100%);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  color: var(--text-primary);
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.4s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.terminal-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.terminal-button:hover::before {
  left: 100%;
}

.terminal-button:hover {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(78, 205, 196, 0.2) 100%);
  border-color: rgba(0, 212, 255, 0.6);
  transform: translateY(-2px);
  box-shadow:
    0 10px 30px rgba(0, 212, 255, 0.3),
    0 0 50px rgba(0, 212, 255, 0.1);
}

.terminal-button.secondary {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(255, 189, 46, 0.1) 100%);
  border-color: rgba(255, 107, 107, 0.3);
}

.terminal-button.secondary:hover {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.2) 0%, rgba(255, 189, 46, 0.2) 100%);
  border-color: rgba(255, 107, 107, 0.6);
  box-shadow:
    0 10px 30px rgba(255, 107, 107, 0.3),
    0 0 50px rgba(255, 107, 107, 0.1);
}

.button-text {
  font-weight: 600;
}

.button-icon {
  font-size: 1.1rem;
}

/* Terminal Footer */
.terminal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid rgba(0, 212, 255, 0.2);
  margin-top: 2rem;
}

.footer-info {
  display: flex;
  gap: 2rem;
  font-size: 0.85rem;
}

.connection-info,
.response-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
}

.connection-info i,
.response-time i {
  color: var(--accent-color);
}

.typing-indicator {
  display: flex;
  align-items: center;
}

.cursor {
  color: var(--primary-color);
  animation: blink 1s infinite;
  font-weight: bold;
}

/* Terminal Background Effects */
.terminal-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
  border-radius: 12px;
}



.scan-lines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    0deg,
    transparent,
    transparent 2px,
    rgba(0, 212, 255, 0.03) 2px,
    rgba(0, 212, 255, 0.03) 4px
  );
  animation: scanMove 2s linear infinite;
}

.screen-flicker {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 212, 255, 0.02);
  animation: flicker 0.15s infinite linear alternate;
}

/* Message Status */
.message-status {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease;
  transform: translateX(100%);
}

.message-status.show {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

.status-content {
  background: rgba(26, 26, 26, 0.95);
  border-radius: 12px;
  border: 1px solid rgba(0, 212, 255, 0.3);
  backdrop-filter: blur(20px);
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.5),
    0 0 50px rgba(0, 212, 255, 0.2);
}

.status-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.status-content.success .status-icon {
  background: var(--accent-color);
  color: var(--text-primary);
}

.status-content.success .status-icon::before {
  content: '✓';
}

.status-content.error .status-icon {
  background: #ff6b6b;
  color: var(--text-primary);
}

.status-content.error .status-icon::before {
  content: '✗';
}

.status-text {
  color: var(--text-primary);
  font-weight: 500;
}

/* Terminal Animations */
@keyframes terminalExpand {
  0% {
    opacity: 0;
    transform: scale(0.1);
  }
  30% {
    opacity: 0.3;
    transform: scale(0.5);
  }
  60% {
    opacity: 0.7;
    transform: scale(0.9);
  }
  80% {
    opacity: 0.9;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes typeIn {
  0% {
    opacity: 0;
    transform: translateX(-20px);
    width: 0;
  }
  50% {
    opacity: 1;
    transform: translateX(0);
    width: 0;
  }
  100% {
    opacity: 1;
    transform: translateX(0);
    width: 100%;
  }
}

@keyframes typeText {
  0% {
    width: 0;
    opacity: 0;
  }
  1% {
    opacity: 1;
  }
  100% {
    width: 100%;
    opacity: 1;
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
}



@keyframes scanMove {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100%);
  }
}

@keyframes flicker {
  0% {
    opacity: 0.02;
  }
  100% {
    opacity: 0.05;
  }
}

/* Terminal Responsive Design */
@media (max-width: 768px) {
  .terminal-container {
    margin: 0 1rem;
  }

  .terminal-window {
    transform: none;
  }

  .terminal-window:hover {
    transform: scale(1.01);
  }

  .terminal-header {
    padding: 0.5rem 0.75rem;
  }

  .terminal-title {
    font-size: 0.8rem;
  }

  .terminal-body {
    padding: 1rem;
    min-height: 400px;
  }

  .output-line {
    flex-direction: column;
    gap: 0.25rem;
  }

  .input-line {
    flex-direction: column;
    gap: 0.25rem;
  }

  .input-prompt {
    margin-top: 0;
  }

  .form-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .terminal-button {
    justify-content: center;
  }

  .footer-info {
    flex-direction: column;
    gap: 0.5rem;
    font-size: 0.8rem;
  }

  .message-status {
    top: 1rem;
    right: 1rem;
    left: 1rem;
  }
}

@media (max-width: 480px) {
  .terminal-body {
    padding: 0.75rem;
    min-height: 350px;
  }

  .terminal-form textarea {
    min-height: 80px;
  }

  .control-button {
    width: 10px;
    height: 10px;
  }

  .terminal-title {
    font-size: 0.75rem;
  }

  .status-text {
    font-size: 0.7rem;
  }
}

/* Evolution Section Styles */
.evolution-container {
  position: relative;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem 0;
  min-height: 100vh;
}

/* Network Background */
.network-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  opacity: 0.6;
}

.network-svg {
  width: 100%;
  height: 100%;
}

.connection-line {
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
  animation: drawNetwork 4s ease-in-out forwards;
}

.connection-line.line-1 { animation-delay: 0.5s; }
.connection-line.line-2 { animation-delay: 1s; }
.connection-line.line-3 { animation-delay: 1.5s; }
.connection-line.line-4 { animation-delay: 2s; }
.connection-line.line-5 { animation-delay: 2.5s; }

.data-particle {
  opacity: 0;
  animation: particleFlow 3s ease-in-out infinite;
}

.particle-1 { animation-delay: 3s; }
.particle-2 { animation-delay: 3.5s; }
.particle-3 { animation-delay: 4s; }
.particle-4 { animation-delay: 4.5s; }
.particle-5 { animation-delay: 5s; }

/* Evolution Stages */
.evolution-stages {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  gap: 4rem;
  padding: 2rem 0;
}

.evolution-stage {
  opacity: 0;
  transform: translateX(-100px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.evolution-stage.animate-in {
  opacity: 1;
  transform: translateX(0);
}

.evolution-stage:nth-child(even) {
  transform: translateX(100px);
}

.evolution-stage:nth-child(even).animate-in {
  transform: translateX(0);
}

.stage-container {
  background: rgba(26, 26, 26, 0.9);
  border-radius: 25px;
  border: 2px solid rgba(0, 212, 255, 0.3);
  backdrop-filter: blur(30px);
  padding: 2.5rem;
  position: relative;
  overflow: hidden;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.evolution-stage {
  cursor: pointer;
}

.stage-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(0, 212, 255, 0.05) 0%,
    rgba(78, 205, 196, 0.05) 50%,
    rgba(255, 107, 107, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.6s ease;
  z-index: 1;
}

.evolution-stage:hover .stage-container::before {
  opacity: 1;
}

.evolution-stage:hover .stage-container {
  transform: translateY(-15px) scale(1.02);
  border-color: rgba(0, 212, 255, 0.8);
  box-shadow:
    0 30px 60px rgba(0, 212, 255, 0.3),
    0 0 100px rgba(0, 212, 255, 0.2);
}

/* Stage Header */
.stage-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
}

.stage-icon {
  width: 80px;
  height: 80px;
  background: var(--gradient-primary);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: var(--text-primary);
  box-shadow:
    0 10px 30px rgba(0, 212, 255, 0.3),
    0 0 50px rgba(0, 212, 255, 0.1);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stage-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    rgba(255, 255, 255, 0.2),
    transparent,
    rgba(255, 255, 255, 0.1));
  opacity: 0;
  transition: opacity 0.4s ease;
}

.evolution-stage:hover .stage-icon {
  transform: scale(1.1) rotate(5deg);
  border-radius: 50%;
}

.evolution-stage:hover .stage-icon::before {
  opacity: 1;
}

.stage-meta {
  flex: 1;
}

.stage-year {
  font-size: 1rem;
  color: var(--primary-color);
  font-weight: 600;
  letter-spacing: 2px;
  margin-bottom: 0.5rem;
  display: block;
}

.stage-title {
  font-size: 2rem;
  font-weight: 800;
  color: var(--text-primary);
  margin: 0;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Expand Button */
.expand-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.4s ease;
  margin-left: auto;
  pointer-events: none; /* Make it non-interactive since whole card is clickable */
}

.evolution-stage:hover .expand-button {
  background: rgba(0, 212, 255, 0.2);
  border-color: rgba(0, 212, 255, 0.6);
  transform: scale(1.1);
}

.expand-button i {
  color: var(--primary-color);
  font-size: 1rem;
  transition: all 0.4s ease;
}

.evolution-stage.expanded .expand-button i {
  transform: rotate(180deg);
}

.evolution-stage.expanded .expand-button {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.evolution-stage.expanded .expand-button i {
  color: var(--text-primary);
}

/* Stage Content */
.stage-content {
  position: relative;
  z-index: 2;
}

/* Company Card */
.company-card {
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 15px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.4s ease;
}

.company-card:hover {
  background: rgba(0, 212, 255, 0.15);
  border-color: rgba(0, 212, 255, 0.5);
  transform: translateY(-5px);
}

.company-logo {
  width: 60px;
  height: 60px;
  background: var(--gradient-primary);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--text-primary);
  box-shadow: 0 5px 20px rgba(0, 212, 255, 0.3);
}

.company-info h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.25rem 0;
}

.company-info p {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin: 0;
}

/* Evolution Details */
.evolution-details {
  margin-top: 2rem;
}

.skill-evolution {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 2rem;
}

.skill-node {
  background: rgba(78, 205, 196, 0.1);
  border: 1px solid rgba(78, 205, 196, 0.3);
  border-radius: 25px;
  padding: 0.8rem 1.2rem;
  position: relative;
  transition: all 0.4s ease;
  cursor: pointer;
}

.skill-node:hover {
  background: rgba(78, 205, 196, 0.2);
  border-color: rgba(78, 205, 196, 0.6);
  transform: translateY(-3px);
}

.skill-name {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--accent-color);
}

.skill-connections {
  position: absolute;
  top: 50%;
  right: -10px;
  width: 20px;
  height: 2px;
  background: var(--accent-color);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.skill-node:hover .skill-connections {
  opacity: 1;
}

/* Expanded Content */
.expanded-content {
  max-height: 0;
  overflow: hidden;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  margin-top: 0;
}

.evolution-stage.expanded .expanded-content {
  max-height: 1000px;
  opacity: 1;
  margin-top: 2rem;
}

.detailed-description {
  background: rgba(0, 212, 255, 0.05);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 15px;
  padding: 2rem;
  color: var(--text-secondary);
  line-height: 1.7;
}

.detailed-description h4 {
  color: var(--text-primary);
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.detailed-description h5 {
  color: var(--primary-color);
  font-size: 1.1rem;
  font-weight: 600;
  margin: 1.5rem 0 0.75rem 0;
}

.detailed-description ul {
  margin: 0.75rem 0 1.5rem 1rem;
  padding: 0;
}

.detailed-description li {
  margin-bottom: 0.5rem;
  position: relative;
}

.detailed-description li strong {
  color: var(--accent-color);
  font-weight: 600;
}

.detailed-description p {
  margin-bottom: 1rem;
}

.detailed-description p:last-child {
  margin-bottom: 0;
}

/* Transformation Arrow */
.transformation-arrow {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
}

.arrow-body {
  flex: 1;
  height: 3px;
  background: linear-gradient(90deg,
    var(--primary-color) 0%,
    var(--accent-color) 100%);
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.arrow-body::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.6) 50%,
    transparent 100%);
  animation: arrowFlow 2s ease-in-out infinite;
}

.arrow-head {
  width: 0;
  height: 0;
  border-left: 15px solid var(--accent-color);
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
}

.transformation-text {
  font-size: 0.8rem;
  color: var(--accent-color);
  font-weight: 600;
  letter-spacing: 1px;
  white-space: nowrap;
}

/* Future Stage Special Styling */
.future-stage .stage-container {
  border: 2px solid rgba(255, 215, 0, 0.4);
  background: rgba(26, 26, 26, 0.95);
}

.future-stage .stage-icon {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: var(--bg-primary);
}

.future-stage:hover .stage-container {
  border-color: rgba(255, 215, 0, 0.8);
  box-shadow:
    0 30px 60px rgba(255, 215, 0, 0.3),
    0 0 100px rgba(255, 215, 0, 0.2);
}

.future-card {
  background: rgba(255, 215, 0, 0.1);
  border-color: rgba(255, 215, 0, 0.3);
}

.future-card .company-logo {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: var(--bg-primary);
}

.future-arrow .arrow-body {
  background: linear-gradient(90deg, #ffd700 0%, #ffed4e 100%);
}

.future-arrow .arrow-head {
  border-left-color: #ffd700;
}

.future-arrow .transformation-text {
  color: #ffd700;
}

/* Evolution Navigator */
.evolution-navigator {
  position: fixed;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.nav-track {
  width: 4px;
  height: 200px;
  background: rgba(0, 212, 255, 0.2);
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.nav-progress {
  width: 100%;
  height: 0%;
  background: var(--gradient-primary);
  border-radius: 2px;
  transition: height 0.6s ease;
}

.nav-dots {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 10px 0;
}

.nav-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(0, 212, 255, 0.3);
  border: 2px solid rgba(0, 212, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.nav-dot:hover {
  background: rgba(0, 212, 255, 0.6);
  transform: scale(1.2);
}

.nav-dot.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.nav-dot.future-dot {
  background: rgba(255, 215, 0, 0.3);
  border-color: rgba(255, 215, 0, 0.5);
}

.nav-dot.future-dot.active {
  background: #ffd700;
  border-color: #ffd700;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

/* Animations */
@keyframes drawNetwork {
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes particleFlow {
  0%, 100% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes arrowFlow {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
