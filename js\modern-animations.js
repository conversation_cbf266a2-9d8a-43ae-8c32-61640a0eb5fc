// Modern Animations with Anime.js

// IMMEDIATE TEST - This should show in console if file loads
console.log('🔥 [IMMEDIATE TEST] modern-animations.js file loaded successfully!');
console.log('🔥 [IMMEDIATE TEST] Timestamp:', new Date().toISOString());
console.log('🔥 [IMMEDIATE TEST] Document readyState:', document.readyState);
console.log('🔥 [IMMEDIATE TEST] Anime.js available:', typeof anime !== 'undefined');

// Remove this DOMContentLoaded listener to prevent conflicts
// All animations are now initialized through initMainAnimations() after preloader

// Preloader Animation
function initPreloader() {
    const preloader = document.getElementById('preloader');
    const loadingProgress = document.querySelector('.loading-progress');
    const logoText = document.querySelector('.logo-text');
    const logoSubtitle = document.querySelector('.logo-subtitle');
    const loadingText = document.querySelector('.loading-text');

    // Animate logo entrance
    anime({
        targets: logoText,
        scale: [0, 1],
        opacity: [0, 1],
        duration: 800,
        easing: 'easeOutElastic(1, .8)',
        delay: 300
    });

    anime({
        targets: logoSubtitle,
        translateY: [30, 0],
        opacity: [0, 1],
        duration: 600,
        easing: 'easeOutQuad',
        delay: 800
    });

    // Loading bar animation
    anime({
        targets: loadingProgress,
        width: '100%',
        duration: 2000,
        easing: 'easeInOutQuad',
        delay: 1000,
        complete: function() {
            // Hide preloader after loading
            setTimeout(() => {
                anime({
                    targets: preloader,
                    opacity: 0,
                    duration: 500,
                    easing: 'easeOutQuad',
                    complete: function() {
                        preloader.style.display = 'none';
                        document.body.classList.add('loaded');
                        initHeroAnimations();
                    }
                });
            }, 500);
        }
    });

    // Loading text animation
    anime({
        targets: loadingText,
        opacity: [0.5, 1, 0.5],
        duration: 1500,
        easing: 'easeInOutSine',
        loop: true,
        delay: 1200
    });
}

// Hero Section Animations
function initHeroAnimations() {
    console.log('🎬 [HERO] Initializing hero animations...');

    // Check if hero animations already initialized AND completed
    if (document.body.hasAttribute('data-hero-animations-completed')) {
        console.log('🎬 [HERO] ❌ Hero animations already completed, skipping...');
        return;
    }

    // Mark as started (not completed yet)
    document.body.setAttribute('data-hero-animations-initialized', 'true');
    console.log('🎬 [HERO] ✅ Marked hero animations as started');

    // Check if elements exist
    const greetingText = document.querySelector('.greeting-text');
    const heroTitle = document.querySelector('.hero-title');
    const heroDescription = document.querySelector('.hero-description');
    const heroButtons = document.querySelectorAll('.hero-actions .btn-primary, .hero-actions .btn-secondary');
    const socialLinks = document.querySelectorAll('.social-link');
    const profileImage = document.querySelector('.profile-image');

    console.log('🎬 [HERO] Element check:');
    console.log('🎬 [HERO] - Greeting text:', !!greetingText);
    console.log('🎬 [HERO] - Hero title:', !!heroTitle);
    console.log('🎬 [HERO] - Hero description:', !!heroDescription);
    console.log('🎬 [HERO] - Hero buttons:', heroButtons.length);
    console.log('🎬 [HERO] - Social links:', socialLinks.length);
    console.log('🎬 [HERO] - Profile image:', !!profileImage);

    // Reset any existing transforms to prevent conflicts
    anime.set(['.greeting-text', '.hero-title', '.hero-description', '.hero-actions .btn-primary', '.hero-actions .btn-secondary', '.social-link', '.profile-image', '.scroll-indicator'], {
        translateY: 0,
        translateX: 0,
        scale: 1,
        opacity: 0
    });

    console.log('🎬 [HERO] Initial state set - all elements should be hidden');

    // Animate hero text elements (excluding name elements to prevent double animation)
    console.log('🎬 [HERO] Starting greeting text animation');
    anime({
        targets: '.greeting-text',
        translateY: [30, 0],
        opacity: [0, 1],
        duration: 600,
        easing: 'easeOutQuad',
        delay: 200,
        complete: function() {
            console.log('🎬 [HERO] ✅ Greeting text animation completed');
        }
    });

    console.log('🎬 [HERO] Starting hero title animation');
    anime({
        targets: '.hero-title',
        translateY: [20, 0],
        opacity: [0, 1],
        duration: 600,
        easing: 'easeOutQuad',
        delay: 400,
        complete: function() {
            console.log('🎬 [HERO] ✅ Hero title animation completed');
        }
    });

    console.log('🎬 [HERO] Starting hero description animation');
    anime({
        targets: '.hero-description',
        translateY: [20, 0],
        opacity: [0, 1],
        duration: 600,
        easing: 'easeOutQuad',
        delay: 600,
        complete: function() {
            console.log('🎬 [HERO] ✅ Hero description animation completed');
        }
    });

    console.log('🎬 [HERO] Starting hero buttons animation');
    anime({
        targets: '.hero-actions .btn-primary, .hero-actions .btn-secondary',
        translateY: [30, 0],
        opacity: [0, 1],
        duration: 600,
        easing: 'easeOutQuad',
        delay: anime.stagger(100, {start: 800}),
        complete: function() {
            console.log('🎬 [HERO] ✅ Hero buttons animation completed');
        }
    });

    console.log('🎬 [HERO] Starting social links animation');
    anime({
        targets: '.social-link',
        scale: [0, 1],
        opacity: [0, 1],
        duration: 500,
        easing: 'easeOutBack(1.7)',
        delay: anime.stagger(100, {start: 1000}),
        complete: function() {
            console.log('🎬 [HERO] ✅ Social links animation completed');
        }
    });

    // Profile image animation
    console.log('🎬 [HERO] Starting profile image animation');
    anime({
        targets: '.profile-image',
        scale: [0.8, 1],
        opacity: [0, 1],
        duration: 1000,
        easing: 'easeOutElastic(1, .8)',
        delay: 400,
        complete: function() {
            console.log('🎬 [HERO] ✅ Profile image animation completed');
        }
    });

    // Scroll indicator animation
    anime({
        targets: '.scroll-indicator',
        translateY: [30, 0],
        opacity: [0, 1],
        duration: 600,
        easing: 'easeOutQuad',
        delay: 1200,
        complete: function() {
            console.log('🎬 [HERO] ✅ Scroll indicator animation completed');
            // Mark hero animations as fully completed
            document.body.setAttribute('data-hero-animations-completed', 'true');
            console.log('🎬 [HERO] 🎉 ALL HERO ANIMATIONS COMPLETED!');
        }
    });

    console.log('🎬 [HERO] ✅ Hero animations initialized successfully');
}

// Typing Animation for Dynamic Title
function initTypingAnimation() {
    console.log('⌨️ [TYPING] Initializing typing animation...');

    // Check if typing animation already initialized
    if (document.body.hasAttribute('data-typing-animation-initialized')) {
        console.log('⌨️ [TYPING] ❌ Typing animation already initialized, skipping...');
        return;
    }

    const dynamicTitle = document.getElementById('dynamic-title');
    if (!dynamicTitle) {
        console.log('⌨️ [TYPING] ❌ Dynamic title element not found');
        return;
    }

    // Mark as initialized
    document.body.setAttribute('data-typing-animation-initialized', 'true');
    console.log('⌨️ [TYPING] ✅ Marked typing animation as initialized');

    const titles = [
        'AI Enthusiast',
        'Web Developer',
        'Automation Expert',
        'Virtual Assistant',
        'Creative Professional'
    ];

    let currentIndex = 0;
    let currentText = '';
    let isDeleting = false;
    let typingTimeout = null;

    function typeText() {
        // Check if we should stop (in case of cleanup)
        if (!document.body.hasAttribute('data-typing-animation-initialized')) {
            return;
        }

        const fullText = titles[currentIndex];

        if (isDeleting) {
            currentText = fullText.substring(0, currentText.length - 1);
        } else {
            currentText = fullText.substring(0, currentText.length + 1);
        }

        if (dynamicTitle) {
            dynamicTitle.textContent = currentText;
        }

        let typeSpeed = isDeleting ? 50 : 100;

        if (!isDeleting && currentText === fullText) {
            typeSpeed = 2000; // Pause at end
            isDeleting = true;
        } else if (isDeleting && currentText === '') {
            isDeleting = false;
            currentIndex = (currentIndex + 1) % titles.length;
            typeSpeed = 500; // Pause before next word
        }

        typingTimeout = setTimeout(typeText, typeSpeed);
    }

    // Start typing animation after preloader (adjusted for faster preloader)
    setTimeout(() => {
        console.log('⌨️ [TYPING] ✅ Starting typing animation');
        typeText();
    }, 1500); // Reduced to 1500ms to match faster preloader
}

// Floating Icons Animation
function initFloatingIcons() {
    anime({
        targets: '.floating-icon',
        translateY: [20, 0],
        opacity: [0, 1],
        scale: [0, 1],
        duration: 800,
        easing: 'easeOutElastic(1, .8)',
        delay: anime.stagger(200, {start: 2000})
    });
}

// Particles Animation
function initParticles() {
    const particlesContainer = document.getElementById('particles');
    const particleCount = 50;
    
    // Create particles
    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.cssText = `
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(0, 212, 255, 0.6);
            border-radius: 50%;
            pointer-events: none;
        `;
        particlesContainer.appendChild(particle);
    }
    
    // Animate particles
    anime({
        targets: '.particle',
        translateX: function() {
            return anime.random(-window.innerWidth, window.innerWidth);
        },
        translateY: function() {
            return anime.random(-window.innerHeight, window.innerHeight);
        },
        scale: function() {
            return anime.random(0.5, 2);
        },
        opacity: function() {
            return anime.random(0.2, 0.8);
        },
        duration: function() {
            return anime.random(10000, 20000);
        },
        easing: 'linear',
        loop: true,
        direction: 'alternate'
    });
}

// Navigation Animations
function initNavigation() {
    const navbar = document.getElementById('navbar');
    const hamburger = document.getElementById('hamburger');
    const navMenu = document.getElementById('nav-menu');
    
    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(10, 10, 10, 0.95)';
        } else {
            navbar.style.background = 'rgba(10, 10, 10, 0.9)';
        }
    });
    
    // Mobile menu toggle
    hamburger.addEventListener('click', function() {
        navMenu.classList.toggle('active');
        hamburger.classList.toggle('active');
    });
    
    // Smooth scroll for navigation links
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Scroll Animations
function initScrollAnimations() {
    // Scroll indicator click
    document.querySelector('.scroll-indicator').addEventListener('click', function() {
        window.scrollTo({
            top: window.innerHeight,
            behavior: 'smooth'
        });
    });
    
    // Parallax effect for hero background
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const parallax = document.querySelector('.hero-background');
        const speed = scrolled * 0.5;
        
        if (parallax) {
            parallax.style.transform = `translateY(${speed}px)`;
        }
    });
}

// Button Hover Animations
document.addEventListener('DOMContentLoaded', function() {
    // Primary button hover effect
    document.querySelectorAll('.btn-primary').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            anime({
                targets: this,
                scale: 1.05,
                duration: 200,
                easing: 'easeOutQuad'
            });
        });
        
        btn.addEventListener('mouseleave', function() {
            anime({
                targets: this,
                scale: 1,
                duration: 200,
                easing: 'easeOutQuad'
            });
        });
    });
    
    // Social links hover effect
    document.querySelectorAll('.social-link').forEach(link => {
        link.addEventListener('mouseenter', function() {
            anime({
                targets: this,
                scale: 1.1,
                rotate: '5deg',
                duration: 200,
                easing: 'easeOutQuad'
            });
        });
        
        link.addEventListener('mouseleave', function() {
            anime({
                targets: this,
                scale: 1,
                rotate: '0deg',
                duration: 200,
                easing: 'easeOutQuad'
            });
        });
    });
});

// Intersection Observer for scroll animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('animate-in');
        }
    });
}, observerOptions);

// Observe elements for scroll animations
document.querySelectorAll('.animate-on-scroll').forEach(el => {
    observer.observe(el);
});

// Card Animations
function initCardAnimations() {
    // Animate cards on scroll
    const cardObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const cards = entry.target.querySelectorAll('.neon-card');

                anime({
                    targets: cards,
                    translateY: [50, 0],
                    opacity: [0, 1],
                    scale: [0.9, 1],
                    duration: 800,
                    easing: 'easeOutElastic(1, .8)',
                    delay: anime.stagger(200)
                });

                // Animate section title
                anime({
                    targets: entry.target.querySelector('.section-title'),
                    translateY: [30, 0],
                    opacity: [0, 1],
                    duration: 600,
                    easing: 'easeOutQuad'
                });

                // Animate section subtitle
                anime({
                    targets: entry.target.querySelector('.section-subtitle'),
                    translateY: [20, 0],
                    opacity: [0, 1],
                    duration: 600,
                    easing: 'easeOutQuad',
                    delay: 200
                });

                cardObserver.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.2,
        rootMargin: '0px 0px -100px 0px'
    });

    // Observe all section containers
    document.querySelectorAll('.section-container').forEach(section => {
        cardObserver.observe(section);
    });
}

// Card Hover Effects
function initCardHoverEffects() {
    document.querySelectorAll('.neon-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            // Add hover class for CSS transitions
            this.classList.add('card-hover');

            // Animate the card lift
            anime({
                targets: this,
                translateY: [0, -15],
                duration: 400,
                easing: 'easeOutQuad'
            });
        });

        card.addEventListener('mouseleave', function() {
            // Remove hover class
            this.classList.remove('card-hover');

            // Reset the card position
            anime({
                targets: this,
                translateY: [-15, 0],
                duration: 400,
                easing: 'easeOutQuad'
            });
        });
    });
}

// Skill Tag Animations
function initSkillTagAnimations() {
    const skillObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const skillTags = entry.target.querySelectorAll('.skill-tag');

                anime({
                    targets: skillTags,
                    scale: [0, 1],
                    opacity: [0, 1],
                    duration: 400,
                    easing: 'easeOutBack(1.7)',
                    delay: anime.stagger(50)
                });

                skillObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    document.querySelectorAll('.skill-tags').forEach(container => {
        skillObserver.observe(container);
    });
}

// Language Progress Bar Animations
function initLanguageProgressAnimations() {
    const languageObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const progressBars = entry.target.querySelectorAll('.language-progress');

                progressBars.forEach(bar => {
                    const width = bar.style.width;
                    bar.style.width = '0%';

                    anime({
                        targets: bar,
                        width: width,
                        duration: 1500,
                        easing: 'easeOutQuad',
                        delay: 500
                    });
                });

                languageObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    document.querySelectorAll('.language-skills').forEach(container => {
        languageObserver.observe(container);
    });
}

// Timeline Animations
function initTimelineAnimations() {
    const timelineObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const timelineItems = entry.target.querySelectorAll('.timeline-item');

                anime({
                    targets: timelineItems,
                    translateX: [-30, 0],
                    opacity: [0, 1],
                    duration: 600,
                    easing: 'easeOutQuad',
                    delay: anime.stagger(200)
                });

                timelineObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.3 });

    document.querySelectorAll('.education-timeline').forEach(timeline => {
        timelineObserver.observe(timeline);
    });
}

// Evolution Section Animations
function initEvolutionAnimations() {
    // Animate evolution stages on scroll
    const evolutionObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const stages = entry.target.querySelectorAll('.evolution-stage');

                stages.forEach((stage, index) => {
                    setTimeout(() => {
                        stage.classList.add('animate-in');

                        // Animate stage components
                        anime({
                            targets: stage.querySelector('.stage-icon'),
                            scale: [0, 1],
                            rotate: [180, 0],
                            duration: 800,
                            easing: 'easeOutElastic(1, .8)',
                            delay: 200
                        });

                        anime({
                            targets: stage.querySelectorAll('.skill-node'),
                            scale: [0, 1],
                            opacity: [0, 1],
                            duration: 600,
                            easing: 'easeOutBack(1.7)',
                            delay: anime.stagger(100, {start: 600})
                        });

                        anime({
                            targets: stage.querySelector('.transformation-arrow'),
                            opacity: [0, 1],
                            scaleX: [0, 1],
                            duration: 800,
                            easing: 'easeOutQuad',
                            delay: 1000
                        });

                    }, index * 300);
                });

                evolutionObserver.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -100px 0px'
    });

    // Observe evolution section
    const evolutionSection = document.querySelector('#journey');
    if (evolutionSection) {
        evolutionObserver.observe(evolutionSection);
    }
}

// Interactive Evolution Functionality
function initInteractiveEvolution() {
    const stages = document.querySelectorAll('.evolution-stage');
    const navDots = document.querySelectorAll('.nav-dot');
    const navProgress = document.querySelector('.nav-progress');

    // Stage click interactions
    stages.forEach((stage, index) => {
        stage.addEventListener('click', function() {
            // Toggle expanded state
            const isExpanded = stage.classList.contains('expanded');

            if (isExpanded) {
                // Collapse
                stage.classList.remove('expanded');

                anime({
                    targets: stage.querySelector('.expanded-content'),
                    maxHeight: [stage.querySelector('.expanded-content').scrollHeight + 'px', '0px'],
                    opacity: [1, 0],
                    duration: 600,
                    easing: 'easeInOutQuad'
                });
            } else {
                // Expand
                stage.classList.add('expanded');

                const expandedContent = stage.querySelector('.expanded-content');
                const targetHeight = expandedContent.scrollHeight;

                anime({
                    targets: expandedContent,
                    maxHeight: ['0px', targetHeight + 'px'],
                    opacity: [0, 1],
                    duration: 600,
                    easing: 'easeOutQuad'
                });

                // Animate detailed content elements
                setTimeout(() => {
                    const detailElements = stage.querySelectorAll('.detailed-description h4, .detailed-description h5, .detailed-description p, .detailed-description li');
                    anime({
                        targets: detailElements,
                        translateY: [20, 0],
                        opacity: [0, 1],
                        duration: 400,
                        easing: 'easeOutQuad',
                        delay: anime.stagger(50)
                    });
                }, 200);
            }

            // Remove active class from all stages and nav dots
            stages.forEach(s => s.classList.remove('active'));
            navDots.forEach(dot => dot.classList.remove('active'));

            // Add active class to clicked stage and corresponding nav dot
            this.classList.add('active');
            if (navDots[index]) {
                navDots[index].classList.add('active');
            }

            // Update navigation progress
            const progressPercent = ((index + 1) / stages.length) * 100;
            if (navProgress) {
                anime({
                    targets: navProgress,
                    height: `${progressPercent}%`,
                    duration: 600,
                    easing: 'easeOutQuad'
                });
            }

            // Animate stage content
            anime({
                targets: this.querySelector('.stage-container'),
                scale: [1, 1.05, 1],
                duration: 600,
                easing: 'easeOutElastic(1, .8)'
            });

            // Animate skill nodes
            const skillNodes = this.querySelectorAll('.skill-node');
            anime({
                targets: skillNodes,
                scale: [1, 1.1, 1],
                duration: 400,
                easing: 'easeOutQuad',
                delay: anime.stagger(100)
            });

            // Animate company card
            anime({
                targets: this.querySelector('.company-card'),
                translateY: [-5, 0],
                duration: 400,
                easing: 'easeOutQuad'
            });
        });

        // Enhanced hover effects
        stage.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                anime({
                    targets: this.querySelector('.stage-icon'),
                    scale: 1.1,
                    rotate: '5deg',
                    duration: 300,
                    easing: 'easeOutQuad'
                });

                anime({
                    targets: this.querySelector('.stage-title'),
                    color: '#00d4ff',
                    duration: 300,
                    easing: 'easeOutQuad'
                });
            }
        });

        stage.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                anime({
                    targets: this.querySelector('.stage-icon'),
                    scale: 1,
                    rotate: '0deg',
                    duration: 300,
                    easing: 'easeOutQuad'
                });

                anime({
                    targets: this.querySelector('.stage-title'),
                    color: '#ffffff',
                    duration: 300,
                    easing: 'easeOutQuad'
                });
            }
        });
    });

    // Navigation dot interactions
    navDots.forEach((dot, index) => {
        dot.addEventListener('click', function() {
            if (stages[index]) {
                stages[index].click();

                // Scroll to stage
                stages[index].scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            }
        });
    });

    // Auto-activate first stage after animations
    setTimeout(() => {
        if (stages.length > 0) {
            stages[0].click();
        }
    }, 3000);
}

// Network Animation
function initNetworkAnimation() {
    const networkObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const connectionLines = entry.target.querySelectorAll('.connection-line');
                const dataParticles = entry.target.querySelectorAll('.data-particle');

                // Reset and animate connection lines
                connectionLines.forEach((line, index) => {
                    line.style.strokeDashoffset = '1000';
                    line.style.animation = 'none';

                    setTimeout(() => {
                        line.style.animation = `drawNetwork 4s ease-in-out forwards`;
                        line.style.animationDelay = `${0.5 + index * 0.5}s`;
                    }, 100);
                });

                // Animate data particles
                dataParticles.forEach((particle, index) => {
                    particle.style.opacity = '0';
                    particle.style.animation = 'none';

                    setTimeout(() => {
                        particle.style.animation = 'particleFlow 3s ease-in-out infinite';
                        particle.style.animationDelay = `${3 + index * 0.5}s`;
                    }, 100);
                });

                networkObserver.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.2
    });

    const networkBackground = document.querySelector('.network-background');
    if (networkBackground) {
        networkObserver.observe(networkBackground);
    }
}

// Portfolio Section Animations
function initPortfolioAnimations() {
    // Animate portfolio cards on scroll
    const portfolioObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const videoCards = entry.target.querySelectorAll('.video-card');
                const linkCards = entry.target.querySelectorAll('.portfolio-link-card');

                // Animate video cards
                anime({
                    targets: videoCards,
                    translateY: [50, 0],
                    opacity: [0, 1],
                    scale: [0.9, 1],
                    duration: 800,
                    easing: 'easeOutElastic(1, .8)',
                    delay: anime.stagger(200)
                });

                // Animate link cards
                anime({
                    targets: linkCards,
                    translateY: [30, 0],
                    opacity: [0, 1],
                    duration: 600,
                    easing: 'easeOutQuad',
                    delay: anime.stagger(150, {start: 600})
                });

                portfolioObserver.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.2,
        rootMargin: '0px 0px -100px 0px'
    });

    // Observe portfolio section
    const portfolioSection = document.querySelector('#portfolio');
    if (portfolioSection) {
        portfolioObserver.observe(portfolioSection);
    }
}

// Video Modal Functionality
function initVideoModal() {
    const videoCards = document.querySelectorAll('.video-card');
    const videoModal = document.getElementById('videoModal');
    const modalClose = document.querySelector('.modal-close');
    const modalBackdrop = document.querySelector('.modal-backdrop');
    const videoPlayer = document.querySelector('.video-player');
    const modalVideoTitle = document.querySelector('.modal-video-title');
    const modalVideoDescription = document.querySelector('.modal-video-description');

    // Video data (you can replace these with actual video URLs)
    const videoData = {
        1: {
            url: 'https://www.youtube.com/embed/dQw4w9WgXcQ', // Replace with actual video URL
            title: 'Custom Dashboard Creation',
            description: 'A comprehensive demonstration of building analytics dashboards for performance tracking and metrics analysis.'
        },
        2: {
            url: 'https://www.youtube.com/embed/dQw4w9WgXcQ', // Replace with actual video URL
            title: 'Process Automation Tool',
            description: 'Showcasing automated workflow solutions that improved efficiency by 300% through intelligent scripting.'
        },
        3: {
            url: 'https://www.youtube.com/embed/dQw4w9WgXcQ', // Replace with actual video URL
            title: 'CRM System Enhancement',
            description: 'Custom tools developed to enhance CRM functionality and improve user experience across multiple platforms.'
        },
        4: {
            url: 'https://www.youtube.com/embed/dQw4w9WgXcQ', // Replace with actual video URL
            title: 'AI-Powered Solutions',
            description: 'Implementation of AI tools for enhanced decision-making and automation in business processes.'
        },
        5: {
            url: 'https://www.youtube.com/embed/dQw4w9WgXcQ', // Replace with actual video URL
            title: 'B2B Marketing Campaign',
            description: 'Strategic marketing campaign showcasing lead generation and client acquisition techniques for B2B markets.'
        },
        6: {
            url: 'https://www.youtube.com/embed/dQw4w9WgXcQ', // Replace with actual video URL
            title: 'Digital Marketing Strategy',
            description: 'Comprehensive digital marketing approach with analytics-driven campaign optimization and performance tracking.'
        }
    };

    // Open video modal
    videoCards.forEach(card => {
        card.addEventListener('click', function() {
            const videoId = this.dataset.video;
            const video = videoData[videoId];

            if (video) {
                videoPlayer.src = video.url;
                modalVideoTitle.textContent = video.title;
                modalVideoDescription.textContent = video.description;

                videoModal.classList.add('active');
                document.body.style.overflow = 'hidden';

                // Animate modal appearance
                anime({
                    targets: videoModal,
                    opacity: [0, 1],
                    duration: 400,
                    easing: 'easeOutQuad'
                });
            }
        });
    });

    // Close video modal
    function closeModal() {
        anime({
            targets: videoModal,
            opacity: [1, 0],
            duration: 400,
            easing: 'easeInQuad',
            complete: function() {
                videoModal.classList.remove('active');
                videoPlayer.src = '';
                document.body.style.overflow = '';
            }
        });
    }

    if (modalClose) {
        modalClose.addEventListener('click', closeModal);
    }

    if (modalBackdrop) {
        modalBackdrop.addEventListener('click', closeModal);
    }

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && videoModal.classList.contains('active')) {
            closeModal();
        }
    });
}

// Portfolio Links Functionality
function initPortfolioLinks() {
    const portfolioLinks = document.querySelectorAll('.portfolio-link-card');

    // Portfolio link URLs
    const linkData = {
        'Complete Portfolio Archive': 'https://drive.google.com/drive/folders/your-folder-id', // Replace with actual Google Drive URL
        'WhatsApp Contact': 'https://wa.me/573012372817',
        'LinkedIn Profile': 'https://www.linkedin.com/in/alex-castro-422b39261',
        'Technical Blog': 'https://yourblog.com' // Replace with actual blog URL
    };

    portfolioLinks.forEach(link => {
        link.addEventListener('click', function() {
            const linkTitle = this.querySelector('.link-title').textContent;
            const url = linkData[linkTitle];

            if (url) {
                // Animate click feedback
                anime({
                    targets: this,
                    scale: [1, 0.95, 1],
                    duration: 200,
                    easing: 'easeOutQuad',
                    complete: function() {
                        window.open(url, '_blank');
                    }
                });
            }
        });

        // Enhanced hover effects
        link.addEventListener('mouseenter', function() {
            anime({
                targets: this.querySelector('.link-icon'),
                scale: 1.1,
                rotate: '5deg',
                duration: 300,
                easing: 'easeOutQuad'
            });
        });

        link.addEventListener('mouseleave', function() {
            anime({
                targets: this.querySelector('.link-icon'),
                scale: 1,
                rotate: '0deg',
                duration: 300,
                easing: 'easeOutQuad'
            });
        });
    });
}

// Terminal Contact Functionality
function initTerminalContact() {
    const contactForm = document.getElementById('contactForm');
    const clearButton = document.getElementById('clearForm');
    const messageStatus = document.getElementById('messageStatus');

    // Form submission
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const email = formData.get('email');
            const subject = formData.get('subject');
            const message = formData.get('message');

            // Show loading state
            showTerminalMessage('Sending message...', 'loading');

            // Simulate sending email (replace with actual email service)
            sendEmail(email, subject, message)
                .then(() => {
                    showTerminalMessage('Message sent successfully! I\'ll get back to you soon.', 'success');
                    contactForm.reset();
                    addTerminalOutput('Message sent successfully!', 'success');
                })
                .catch((error) => {
                    showTerminalMessage('Failed to send message. Please try again.', 'error');
                    addTerminalOutput('Error: ' + error.message, 'error');
                });
        });
    }

    // Clear form
    if (clearButton) {
        clearButton.addEventListener('click', function() {
            contactForm.reset();
            addTerminalOutput('Buffer cleared.', 'system');

            // Animate clear action
            anime({
                targets: '.terminal-form input, .terminal-form textarea',
                scale: [1, 0.95, 1],
                duration: 300,
                easing: 'easeOutQuad'
            });
        });
    }
}

// Initialize EmailJS
function initEmailJS() {
    // Initialize EmailJS with your public key
    // You need to replace 'YOUR_PUBLIC_KEY' with your actual EmailJS public key
    // Get it from: https://dashboard.emailjs.com/admin/account
    emailjs.init('YOUR_PUBLIC_KEY'); // Replace with your actual public key
}

// Email sending function using EmailJS
function sendEmail(email, subject, message) {
    return new Promise((resolve, reject) => {
        // EmailJS service configuration
        // You need to set up these values in your EmailJS dashboard:
        const serviceID = 'YOUR_SERVICE_ID'; // Replace with your EmailJS service ID
        const templateID = 'YOUR_TEMPLATE_ID'; // Replace with your EmailJS template ID

        const templateParams = {
            from_email: email,
            from_name: email.split('@')[0], // Extract name from email
            subject: subject,
            message: message,
            to_email: '<EMAIL>' // Replace with your actual email
        };

        emailjs.send(serviceID, templateID, templateParams)
            .then((response) => {
                console.log('Email sent successfully:', response);
                resolve(response);
            })
            .catch((error) => {
                console.error('Email sending failed:', error);
                reject(error);
            });
    });
}

// Show status message
function showTerminalMessage(text, type) {
    const messageStatus = document.getElementById('messageStatus');
    const statusContent = messageStatus.querySelector('.status-content');
    const statusText = messageStatus.querySelector('.status-text');

    statusText.textContent = text;
    statusContent.className = `status-content ${type}`;

    messageStatus.classList.add('show');

    // Auto hide after 5 seconds
    setTimeout(() => {
        messageStatus.classList.remove('show');
    }, 5000);
}

// Add output to terminal
function addTerminalOutput(text, type = 'system') {
    const terminalOutput = document.querySelector('.terminal-output');
    const outputLine = document.createElement('div');
    outputLine.className = `output-line ${type}-message`;

    const timestamp = new Date().toLocaleTimeString();
    outputLine.innerHTML = `
        <span class="timestamp">[${timestamp}]</span>
        <span class="message">${text}</span>
    `;

    terminalOutput.appendChild(outputLine);

    // Animate new line
    anime({
        targets: outputLine,
        opacity: [0, 1],
        translateX: [-20, 0],
        duration: 500,
        easing: 'easeOutQuad'
    });

    // Scroll to bottom
    terminalOutput.scrollTop = terminalOutput.scrollHeight;
}

// Terminal animations on scroll
function initTerminalAnimations() {
    const terminalObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                startTerminalSequence();
                terminalObserver.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.3,
        rootMargin: '0px 0px -100px 0px'
    });

    const contactSection = document.querySelector('#contact');
    if (contactSection) {
        terminalObserver.observe(contactSection);
    }
}

// Terminal sequence with smooth animations
function startTerminalSequence() {
    const terminal = document.querySelector('.terminal-window');
    const outputLines = document.querySelectorAll('.output-line');
    const terminalForm = document.querySelector('.terminal-form');

    if (!terminal) return;

    // Step 1: Terminal expansion animation
    anime({
        targets: terminal,
        scale: [0.1, 1.05, 1],
        opacity: [0, 1],
        duration: 1500,
        easing: 'easeOutElastic(1, .8)',
        complete: function() {
            // Step 2: Start typing sequence after terminal is expanded
            startTypingSequence(outputLines, terminalForm);
        }
    });
}

// Typing sequence for terminal output
function startTypingSequence(outputLines, terminalForm) {
    let currentLine = 0;

    function typeNextLine() {
        if (currentLine >= outputLines.length) {
            // All lines typed, show the form
            showTerminalForm(terminalForm);
            return;
        }

        const line = outputLines[currentLine];
        const prompt = line.querySelector('.prompt');
        const command = line.querySelector('.command');
        const timestamp = line.querySelector('.timestamp');
        const message = line.querySelector('.message');

        // Show the line first
        anime({
            targets: line,
            opacity: [0, 1],
            translateX: [-20, 0],
            duration: 300,
            easing: 'easeOutQuad',
            complete: function() {
                // Type the content
                if (command) {
                    typeText(command, 50, function() {
                        currentLine++;
                        setTimeout(typeNextLine, 300);
                    });
                } else if (message) {
                    typeText(message, 30, function() {
                        currentLine++;
                        setTimeout(typeNextLine, 500);
                    });
                } else {
                    currentLine++;
                    setTimeout(typeNextLine, 200);
                }
            }
        });
    }

    // Start typing the first line after a short delay
    setTimeout(typeNextLine, 500);
}

// Type text character by character
function typeText(element, speed, callback) {
    const text = element.textContent;
    element.textContent = '';
    element.style.borderRight = '2px solid var(--primary-color)'; // Cursor

    let i = 0;
    const timer = setInterval(function() {
        element.textContent += text.charAt(i);
        i++;

        if (i >= text.length) {
            clearInterval(timer);
            element.style.borderRight = 'none'; // Remove cursor
            if (callback) callback();
        }
    }, speed);
}

// Show terminal form
function showTerminalForm(terminalForm) {
    if (!terminalForm) return;

    anime({
        targets: terminalForm,
        opacity: [0, 1],
        translateY: [30, 0],
        duration: 800,
        easing: 'easeOutQuad',
        delay: 500
    });
}

// Terminal typing effects
function initTerminalTyping() {
    const inputs = document.querySelectorAll('.terminal-form input, .terminal-form textarea');

    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            // Add typing indicator
            const cursor = document.querySelector('.cursor');
            if (cursor) {
                cursor.style.animationDuration = '0.5s';
            }
        });

        input.addEventListener('blur', function() {
            // Reset cursor
            const cursor = document.querySelector('.cursor');
            if (cursor) {
                cursor.style.animationDuration = '1s';
            }
        });

        input.addEventListener('input', function() {
            // Add terminal-like typing sound effect (optional)
            // You could add a subtle audio feedback here
        });
    });
}

// Mouse Trail Particle System
function initMouseTrailParticles() {
    console.log('🖱️ [PARTICLES] Initializing mouse trail particles...');

    // Check if already initialized
    if (document.body.hasAttribute('data-mouse-particles-initialized')) {
        console.log('🖱️ [PARTICLES] ❌ Already initialized, skipping...');
        return;
    }

    const canvas = document.getElementById('particleCanvas');
    if (!canvas) {
        console.log('🖱️ [PARTICLES] ❌ Canvas element not found');
        return;
    }

    // Mark as initialized
    document.body.setAttribute('data-mouse-particles-initialized', 'true');
    console.log('🖱️ [PARTICLES] ✅ Marked as initialized');

    const ctx = canvas.getContext('2d');
    const particles = [];
    const maxParticles = 50;
    let mouse = { x: 0, y: 0 };

    // Set canvas size
    function resizeCanvas() {
        canvas.width = canvas.offsetWidth;
        canvas.height = canvas.offsetHeight;
    }

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Particle class
    class Particle {
        constructor(x, y) {
            this.x = x;
            this.y = y;
            this.size = Math.random() * 3 + 1;
            this.speedX = (Math.random() - 0.5) * 2;
            this.speedY = (Math.random() - 0.5) * 2;
            this.life = 1;
            this.decay = Math.random() * 0.02 + 0.01;
            this.color = `hsl(${180 + Math.random() * 60}, 70%, 60%)`;
        }

        update() {
            this.x += this.speedX;
            this.y += this.speedY;
            this.life -= this.decay;
            this.size *= 0.99;
            this.speedX *= 0.98;
            this.speedY *= 0.98;
        }

        draw() {
            ctx.save();
            ctx.globalAlpha = this.life;
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
            ctx.fillStyle = this.color;
            ctx.fill();

            // Add glow effect
            ctx.shadowBlur = 10;
            ctx.shadowColor = this.color;
            ctx.fill();
            ctx.restore();
        }
    }

    // Mouse move handler
    function handleMouseMove(e) {
        const rect = canvas.getBoundingClientRect();
        mouse.x = e.clientX - rect.left;
        mouse.y = e.clientY - rect.top;

        // Create new particles
        for (let i = 0; i < 3; i++) {
            if (particles.length < maxParticles) {
                particles.push(new Particle(
                    mouse.x + (Math.random() - 0.5) * 10,
                    mouse.y + (Math.random() - 0.5) * 10
                ));
            }
        }
    }

    // Animation loop
    function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Update and draw particles
        for (let i = particles.length - 1; i >= 0; i--) {
            particles[i].update();
            particles[i].draw();

            // Remove dead particles
            if (particles[i].life <= 0 || particles[i].size <= 0.1) {
                particles.splice(i, 1);
            }
        }

        requestAnimationFrame(animate);
    }

    // Start the system
    const heroSection = document.getElementById('hero');
    if (heroSection) {
        // Check if mousemove listener already exists to prevent duplicates
        if (!heroSection.hasAttribute('data-mouse-trail-initialized')) {
            heroSection.addEventListener('mousemove', handleMouseMove);
            heroSection.setAttribute('data-mouse-trail-initialized', 'true');
        }
    }

    animate();
}

// Gradient Text Animation
function initGradientTextAnimation() {
    console.log('🎭 [GRADIENT] Initializing gradient text animation...');

    // Check if already initialized
    if (document.body.hasAttribute('data-gradient-text-initialized')) {
        console.log('🎭 [GRADIENT] ❌ Already initialized, skipping...');
        return;
    }

    const gradientTexts = document.querySelectorAll('.gradient-text');
    console.log('🎭 [GRADIENT] Found gradient text elements:', gradientTexts.length);

    if (!gradientTexts.length) {
        console.log('🎭 [GRADIENT] ❌ No gradient text elements found');
        return;
    }

    // Mark as initialized FIRST to prevent any race conditions
    document.body.setAttribute('data-gradient-text-initialized', 'true');
    console.log('🎭 [GRADIENT] ✅ Marked as initialized');

    // Enhanced gradient animation with Anime.js
    function createGradientFlow() {
        gradientTexts.forEach((text, index) => {
            // Create dynamic gradient positions
            anime({
                targets: text,
                backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
                duration: 6000 + (index * 1000), // Stagger the animations
                easing: 'easeInOutSine',
                loop: true,
                delay: index * 500 // Delay each text element
            });
        });
    }

    // Hover effect for gradient text
    function addHoverEffects() {
        gradientTexts.forEach(text => {
            // Check if hover effects already added
            if (!text.hasAttribute('data-hover-initialized')) {
                text.addEventListener('mouseenter', function() {
                    anime({
                        targets: this,
                        scale: [1, 1.05],
                        duration: 400,
                        easing: 'easeOutQuad'
                    });

                    // Removed gradient flow animation to prevent repetitive movement
                });

                text.addEventListener('mouseleave', function() {
                    anime({
                        targets: this,
                        scale: [null, 1],
                        duration: 400,
                        easing: 'easeOutQuad'
                    });
                });

                text.setAttribute('data-hover-initialized', 'true');
            }
        });
    }

    // Name entrance animation (handles both position and gradient) - RUNS ONLY ONCE
    function createNameEntranceAnimation() {
        console.log('🎬 [ENTRANCE] Creating name entrance animation...');

        // TEMPORARILY DISABLED - Check if animation already ran
        // if (document.body.hasAttribute('data-name-entrance-completed')) {
        //     console.log('🎬 [ENTRANCE] ❌ Animation already completed, skipping...');
        //     return;
        // }

        console.log('🎬 [ENTRANCE] ⚠️ ENTRANCE ANIMATION GUARDS DISABLED FOR TESTING');

        // Check elements exist with detailed debugging
        const nameFirst = document.querySelector('.name-first.gradient-text');
        const nameLast = document.querySelector('.name-last.gradient-text');

        console.log('🎬 [ENTRANCE] nameFirst element:', nameFirst);
        console.log('🎬 [ENTRANCE] nameLast element:', nameLast);
        console.log('🎬 [ENTRANCE] Available .name-first elements:', document.querySelectorAll('.name-first'));
        console.log('🎬 [ENTRANCE] Available .name-last elements:', document.querySelectorAll('.name-last'));
        console.log('🎬 [ENTRANCE] Available .gradient-text elements:', document.querySelectorAll('.gradient-text'));

        if (!nameFirst || !nameLast) {
            console.log('🎬 [ENTRANCE] ❌ Required elements not found! Retrying in 100ms...');

            // Retry after a short delay in case elements aren't ready yet
            setTimeout(() => {
                createNameEntranceAnimation();
            }, 100);
            return;
        }

        console.log('🎬 [ENTRANCE] ✅ Elements found, proceeding with animation');

        // Set initial hidden state immediately
        console.log('🎬 [ENTRANCE] Setting initial hidden state...');
        anime.set([nameFirst, nameLast], {
            translateX: 0,
            translateY: 0,
            scale: 1,
            opacity: 0 // Make sure they start hidden
        });

        // Set specific initial positions for dramatic entrance
        anime.set(nameFirst, {
            translateX: -50,
            opacity: 0,
            scale: 0.8
        });

        anime.set(nameLast, {
            translateX: 50,
            opacity: 0,
            scale: 0.8
        });

        console.log('🎬 [ENTRANCE] Initial state set - elements should be hidden');

        console.log('🎬 [ENTRANCE] 🎬 Starting entrance animation...');

        // Mark as completed to prevent race conditions
        document.body.setAttribute('data-name-entrance-completed', 'true');
        console.log('🎬 [ENTRANCE] ✅ Marked as completed to prevent duplicates');

        // Start animation immediately since we're called after preloader
        console.log('🎬 [ENTRANCE] 🚀 Executing entrance animation now!');

        // Animate entrance - ONLY ONCE
        anime.timeline()
            .add({
                targets: nameFirst,
                translateX: [-50, 0],
                scale: [0.8, 1.1, 1],
                opacity: [0, 1],
                duration: 1200,
                easing: 'easeOutElastic(1, .8)',
                delay: 200,
                complete: function() {
                    console.log('🎬 [ENTRANCE] ✅ nameFirst animation completed');
                }
            })
            .add({
                targets: nameLast,
                translateX: [50, 0],
                scale: [0.8, 1.1, 1],
                opacity: [0, 1],
                duration: 1200,
                easing: 'easeOutElastic(1, .8)',
                delay: 100,
                complete: function() {
                    console.log('🎬 [ENTRANCE] ✅ nameLast animation completed');
                    console.log('🎬 [ENTRANCE] 🎉 Full entrance animation sequence completed!');
                }
            }, '-=1000');
    }

    // Initialize name entrance animation
    createNameEntranceAnimation();

    console.log('🎭 [GRADIENT] ✅ Gradient text initialization complete');
}

// Wave Distortion Effect
function initWaveDistortion() {
    console.log('🌊 [WAVE] Initializing wave distortion...');

    // Check if already initialized
    if (document.body.hasAttribute('data-wave-distortion-initialized')) {
        console.log('🌊 [WAVE] ❌ Already initialized, skipping...');
        return;
    }

    const wave = document.querySelector('.wave');
    const heroText = document.querySelector('.hero-text');
    const heroSection = document.getElementById('hero');

    console.log('🌊 [WAVE] Wave element found:', !!wave);
    console.log('🌊 [WAVE] Hero text found:', !!heroText);
    console.log('🌊 [WAVE] Hero section found:', !!heroSection);

    if (!wave) {
        console.log('🌊 [WAVE] ❌ Wave element not found - cannot initialize wave animations');
        return;
    }

    // Mark as initialized
    document.body.setAttribute('data-wave-distortion-initialized', 'true');
    console.log('🌊 [WAVE] ✅ Marked as initialized');

    let mouseX = 0;
    let mouseY = 0;
    let waveAnimation;

    // Create dynamic wave animation using Anime.js
    function createWaveAnimation() {
        console.log('🌊 [WAVE] Creating dynamic wave animation');

        // More dynamic wave states for interesting movement
        const waveStates = [
            'M0,350 Q300,300 600,350 T1200,350 L1200,600 L0,600 Z',
            'M0,350 Q300,400 600,320 T1200,380 L1200,600 L0,600 Z',
            'M0,350 Q300,320 600,380 T1200,340 L1200,600 L0,600 Z',
            'M0,350 Q300,380 600,340 T1200,360 L1200,600 L0,600 Z',
            'M0,350 Q300,340 600,360 T1200,320 L1200,600 L0,600 Z',
            'M0,350 Q300,360 600,330 T1200,370 L1200,600 L0,600 Z'
        ];

        let currentState = 0;
        let isAnimating = true;

        function animateToNextState() {
            if (!isAnimating) return;

            const nextState = (currentState + 1) % waveStates.length;

            waveAnimation = anime({
                targets: wave,
                d: waveStates[nextState],
                duration: 6000, // Slightly faster for more dynamic feel
                easing: 'easeInOutSine',
                complete: function() {
                    currentState = nextState;
                    animateToNextState();
                }
            });
        }

        // Start the animation
        animateToNextState();

        // Store reference to stop animation if needed
        window.stopWaveAnimation = () => { isAnimating = false; };
        window.startWaveAnimation = () => {
            isAnimating = true;
            animateToNextState();
        };
    }

    // Enhanced mouse interaction effects
    function handleWaveMouseMove(e) {
        if (!heroSection) return;

        const rect = heroSection.getBoundingClientRect();
        mouseX = (e.clientX - rect.left) / rect.width;
        mouseY = (e.clientY - rect.top) / rect.height;

        // Create subtle ripple effect on mouse move
        createSubtleRipple(mouseX, mouseY);

        // Add subtle hero text movement based on mouse position
        if (heroText) {
            const moveX = (mouseX - 0.5) * 10; // Subtle horizontal movement
            const moveY = (mouseY - 0.5) * 5;  // Subtle vertical movement

            anime({
                targets: heroText,
                translateX: moveX,
                translateY: moveY,
                duration: 800,
                easing: 'easeOutQuad'
            });
        }
    }

    // Hover effect - simple brightness increase
    function handleMouseEnter() {
        anime({
            targets: wave,
            opacity: [0.7, 0.9],
            duration: 800,
            easing: 'easeOutQuad'
        });
    }

    // Mouse leave - return to normal
    function handleMouseLeave() {
        anime({
            targets: wave,
            opacity: [null, 0.7],
            duration: 800,
            easing: 'easeOutQuad'
        });

        // Don't reset text position here - let mouse trail handle it
    }

    // Click effect - cyberpunk glitch
    function handleMouseClick(e) {
        if (!heroSection) return;

        const rect = heroSection.getBoundingClientRect();
        const clickX = (e.clientX - rect.left) / rect.width;
        const clickY = (e.clientY - rect.top) / rect.height;

        // Create explosive ripple effect
        createExplosiveRipple(clickX, clickY);

        // Flash effect
        createFlashEffect();

        // Trigger cyberpunk glitch effect
        createCyberpunkGlitch();
    }

    // Create enhanced subtle ripple effect for mouse move
    function createSubtleRipple(x, y) {
        const intensity = 12;
        const offsetX = intensity * Math.sin(x * Math.PI * 2);
        const offsetY = intensity * Math.cos(y * Math.PI * 2);

        anime({
            targets: wave,
            translateY: [null, -offsetY, 0],
            scaleX: [1, 1 + (x - 0.5) * 0.05, 1],
            duration: 1200,
            easing: 'easeOutQuad'
        });

        // Add subtle glow effect
        anime({
            targets: wave,
            filter: [
                'brightness(1)',
                `brightness(${1 + y * 0.2}) hue-rotate(${x * 30}deg)`,
                'brightness(1)'
            ],
            duration: 800,
            easing: 'easeOutQuad'
        });
    }

    // Create enhanced explosive ripple effect for clicks
    function createExplosiveRipple(x, y) {
        const intensity = 35;
        const horizontalSpread = 20;

        // Main wave distortion
        anime({
            targets: wave,
            translateY: [0, -intensity, 0],
            scaleY: [1, 1.3, 1],
            scaleX: [1, 1 + (x - 0.5) * 0.2, 1],
            duration: 1500,
            easing: 'easeOutElastic(1, .6)'
        });

        // Color burst effect
        anime({
            targets: wave,
            filter: [
                'brightness(1) saturate(1)',
                `brightness(1.5) saturate(2) hue-rotate(${x * 180}deg)`,
                'brightness(1) saturate(1)'
            ],
            duration: 1000,
            easing: 'easeOutQuad'
        });
    }

    // Create enhanced flash effect for clicks
    function createFlashEffect() {
        // Create temporary flash overlay with multiple layers
        const flash = document.createElement('div');
        flash.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle,
                rgba(0,212,255,0.4) 0%,
                rgba(78,205,196,0.3) 30%,
                rgba(255,107,107,0.2) 60%,
                transparent 80%);
            pointer-events: none;
            z-index: 10;
            opacity: 0;
            mix-blend-mode: screen;
        `;

        heroSection.appendChild(flash);

        // Create secondary flash for more impact
        const flash2 = document.createElement('div');
        flash2.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg,
                rgba(0,212,255,0.1) 0%,
                transparent 50%,
                rgba(78,205,196,0.1) 100%);
            pointer-events: none;
            z-index: 11;
            opacity: 0;
            mix-blend-mode: overlay;
        `;

        heroSection.appendChild(flash2);

        // Animate main flash
        anime({
            targets: flash,
            opacity: [0, 1, 0],
            scale: [0.3, 1.5, 1],
            duration: 1000,
            easing: 'easeOutQuad'
        });

        // Animate secondary flash
        anime({
            targets: flash2,
            opacity: [0, 0.8, 0],
            rotate: [0, 180],
            duration: 1200,
            easing: 'easeOutQuad',
            complete: function() {
                heroSection.removeChild(flash);
                heroSection.removeChild(flash2);
            }
        });
    }

    // Create cyberpunk glitch effect (with throttling) - RE-ENABLED
    let glitchCooldown = false;
    function createCyberpunkGlitch() {
        // Prevent rapid-fire glitch effects
        if (glitchCooldown) return;
        glitchCooldown = true;

        console.log('🎆 [GLITCH] Creating cyberpunk glitch effect');

        // Apply glitch to hero content elements
        const heroName = document.querySelector('.hero-name');
        const heroTitle = document.querySelector('.hero-title');
        const heroDescription = document.querySelector('.hero-description');
        const profileImage = document.querySelector('.profile-glitch');

        // Reset cooldown after animation
        setTimeout(() => {
            glitchCooldown = false;
        }, 1200);

        // Glitch effect for hero name
        if (heroName) {
            anime({
                targets: heroName,
                translateX: [0, -5, 5, -3, 3, 0],
                translateY: [0, 2, -2, 1, -1, 0],
                duration: 300,
                easing: 'steps(6)',
                complete: function() {
                    // RGB split effect
                    anime({
                        targets: heroName,
                        textShadow: [
                            '0 0 0 transparent',
                            '2px 0 0 #ff0000, -2px 0 0 #00ffff',
                            '0 0 0 transparent'
                        ],
                        duration: 200,
                        easing: 'steps(3)'
                    });
                }
            });
        }

        // Glitch effect for hero title
        if (heroTitle) {
            anime({
                targets: heroTitle,
                translateX: [0, 3, -3, 2, -2, 0],
                skewX: [0, 2, -2, 1, -1, 0],
                duration: 250,
                easing: 'steps(5)',
                delay: 100
            });
        }

        // Glitch effect for hero description
        if (heroDescription) {
            anime({
                targets: heroDescription,
                opacity: [1, 0.8, 1, 0.9, 1],
                filter: [
                    'contrast(1)',
                    'contrast(1.2) brightness(1.1)',
                    'contrast(0.9) brightness(0.9)',
                    'contrast(1.1)',
                    'contrast(1)'
                ],
                duration: 400,
                easing: 'steps(5)',
                delay: 150
            });
        }

        // Trigger profile image glitch
        if (profileImage) {
            profileImage.classList.add('active');

            // RGB split effect
            anime({
                targets: profileImage,
                filter: [
                    'hue-rotate(0deg) saturate(1)',
                    'hue-rotate(90deg) saturate(2)',
                    'hue-rotate(180deg) saturate(1.5)',
                    'hue-rotate(0deg) saturate(1)'
                ],
                duration: 400,
                easing: 'steps(4)',
                complete: function() {
                    setTimeout(() => {
                        profileImage.classList.remove('active');
                    }, 100);
                }
            });
        }

        // Create scan lines effect
        createScanLines();
    }

    // Create scan lines effect
    function createScanLines() {
        const scanLine = document.createElement('div');
        scanLine.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #00ffff, transparent);
            pointer-events: none;
            z-index: 15;
            opacity: 0.8;
        `;

        heroSection.appendChild(scanLine);

        // Animate scan line
        anime({
            targets: scanLine,
            translateY: [0, heroSection.offsetHeight],
            opacity: [0.8, 0.8, 0],
            duration: 600,
            easing: 'easeInQuad',
            complete: function() {
                heroSection.removeChild(scanLine);
            }
        });
    }

    // Scroll-based wave behavior
    function handleWaveScroll() {
        const scrollY = window.scrollY;
        const heroHeight = heroSection ? heroSection.offsetHeight : window.innerHeight;
        const scrollProgress = Math.min(scrollY / heroHeight, 1);

        if (wave) {
            // Wave moves down and fades as user scrolls
            const translateY = scrollProgress * 100; // Move wave down
            const opacity = Math.max(0.7 - scrollProgress * 0.7, 0); // Fade out wave
            const scaleY = Math.max(1 - scrollProgress * 0.3, 0.7); // Compress wave

            anime({
                targets: wave,
                translateY: translateY,
                opacity: opacity,
                scaleY: scaleY,
                duration: 100,
                easing: 'easeOutQuad'
            });
        }

        // Also affect hero content with parallax
        if (heroText) {
            const parallaxOffset = scrollProgress * 50;
            anime({
                targets: heroText,
                translateY: -parallaxOffset,
                duration: 100,
                easing: 'easeOutQuad'
            });
        }
    }

    // Initialize wave animations
    console.log('🌊 [WAVE] About to call createWaveAnimation()');
    createWaveAnimation();
    console.log('🌊 [WAVE] createWaveAnimation() called');

    // Add mouse interaction (prevent duplicate listeners)
    if (heroSection && !heroSection.hasAttribute('data-wave-initialized')) {
        heroSection.addEventListener('mousemove', handleWaveMouseMove);
        heroSection.addEventListener('mouseenter', handleMouseEnter);
        heroSection.addEventListener('mouseleave', handleMouseLeave);
        heroSection.addEventListener('click', handleMouseClick);

        // Prevent text selection on rapid clicks
        heroSection.addEventListener('selectstart', function(e) {
            e.preventDefault();
        });

        // Add scroll listener for wave behavior
        window.addEventListener('scroll', handleWaveScroll, { passive: true });

        heroSection.setAttribute('data-wave-initialized', 'true');
        console.log('🌊 [WAVE] ✅ Wave interactions and scroll behavior initialized');
    }
}

// Legacy Orbital Preloader (replaced by modern preloader)
function initOrbitalPreloader() {
    console.log('Legacy preloader disabled - using modern preloader instead');
    return;
    const preloader = document.getElementById('preloader');
    const decodedName = document.getElementById('decodedName');
    const decodingOverlay = document.getElementById('decodingOverlay');
    const orbitContainer = document.getElementById('orbitContainer');
    const progressFill = document.getElementById('progressFill');
    const loadingText = document.getElementById('loadingText');
    const loadingPercentage = document.getElementById('loadingPercentage');
    const loadingStatus = document.getElementById('loadingStatus');
    const loadingStatusText = document.getElementById('loadingStatusText');
    const skipButton = document.getElementById('skipPreloader');

    if (!preloader) return;

    // Configuration
    const config = {
        maxDuration: 2500, // Reduced from 4500ms
        targetText: 'LOADING',
        characters: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789@#$%&*',
        reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches
    };

    // State management
    let state = {
        progress: 0,
        isSkipped: false,
        isCompleted: false,
        intervals: [],
        timeouts: [],
        resourcesLoaded: 0,
        totalResources: 0
    };

    // Loading messages
    const loadingMessages = [
        'INITIALIZING SYSTEMS...',
        'LOADING RESOURCES...',
        'CALIBRATING INTERFACE...',
        'DECODING IDENTITY...',
        'ESTABLISHING CONNECTION...',
        'READY TO LAUNCH...'
    ];

    // Cleanup function
    function cleanup() {
        state.intervals.forEach(id => clearInterval(id));
        state.timeouts.forEach(id => clearTimeout(id));
        state.intervals = [];
        state.timeouts = [];
    }

    // Skip functionality
    function skipPreloader() {
        if (state.isCompleted) return;
        state.isSkipped = true;
        cleanup();
        completePreloader();
    }

    // Resource loading monitoring
    function initResourceMonitoring() {
        const resources = [
            ...document.querySelectorAll('img'),
            ...document.querySelectorAll('link[rel="stylesheet"]'),
            ...document.querySelectorAll('script[src]')
        ];

        state.totalResources = resources.length;

        if (state.totalResources === 0) {
            state.resourcesLoaded = 1;
            return Promise.resolve();
        }

        return new Promise((resolve) => {
            let loadedCount = 0;

            const checkComplete = () => {
                loadedCount++;
                state.resourcesLoaded = loadedCount / state.totalResources;

                if (loadedCount >= state.totalResources) {
                    resolve();
                }
            };

            resources.forEach(resource => {
                if (resource.complete || resource.readyState === 'complete') {
                    checkComplete();
                } else {
                    resource.addEventListener('load', checkComplete);
                    resource.addEventListener('error', checkComplete);
                }
            });

            // Fallback timeout
            const timeoutId = setTimeout(() => {
                state.resourcesLoaded = 1;
                resolve();
            }, 3000);
            state.timeouts.push(timeoutId);
        });
    }

    // Start the preloader sequence
    function startPreloaderSequence() {
        // Skip button event
        skipButton.addEventListener('click', skipPreloader);

        // Start resource monitoring
        initResourceMonitoring().then(() => {
            if (!state.isSkipped) {
                updateLoadingStatus('Resources loaded');
            }
        });

        if (config.reducedMotion) {
            // Simplified sequence for reduced motion
            startSimplifiedSequence();
        } else {
            // Full animation sequence
            startFullSequence();
        }
    }

    function startSimplifiedSequence() {
        // Show elements immediately without complex animations
        anime.set('.orbit', { opacity: 1, transform: 'translate(-50%, -50%) scale(1)' });
        anime.set('.skill-icon', { opacity: 1, transform: 'scale(1)' });

        startDecoding();
        startProgressAnimation();

        const timeoutId = setTimeout(() => completePreloader(), config.maxDuration);
        state.timeouts.push(timeoutId);
    }

    function startFullSequence() {
        // 1. Start 3D orbit approach
        expandOrbits();

        // 2. Start decoding animation when orbits are approaching
        const timeout1 = setTimeout(() => startDecoding(), 600);
        state.timeouts.push(timeout1);

        // 3. Start progress animation
        const timeout2 = setTimeout(() => startProgressAnimation(), 800);
        state.timeouts.push(timeout2);

        // 4. Complete after reduced duration
        const timeout3 = setTimeout(() => completePreloader(), config.maxDuration);
        state.timeouts.push(timeout3);
    }

    // Update loading status
    function updateLoadingStatus(message) {
        if (loadingStatusText) {
            loadingStatusText.textContent = message;
        }
    }

    // 3D Orbit approach animation (optimized)
    function expandOrbits() {
        const orbits = document.querySelectorAll('.orbit');
        const skillIcons = document.querySelectorAll('.skill-icon');

        // Error handling
        if (!orbits.length) return;

        // Orbits start far away in 3D space
        anime.set(orbits, {
            translateZ: -500,
            scale: 0.1,
            opacity: 0
        });

        anime.set(skillIcons, {
            scale: 0,
            opacity: 0,
            translateZ: 0
        });

        // Reduced animation durations for better performance
        const baseAnimation = {
            translateZ: [-500, 0],
            scale: [0.1, 1],
            opacity: [0, 1],
            easing: 'easeOutCubic'
        };

        // Mobile optimization
        const isMobile = window.innerWidth <= 768;
        const duration = isMobile ? 800 : 1200;

        anime({
            targets: '.orbit-inner',
            ...baseAnimation,
            rotateY: [0, 180],
            duration: duration,
            delay: 0
        });

        anime({
            targets: '.orbit-middle',
            ...baseAnimation,
            rotateY: [0, 180],
            duration: duration + 100,
            delay: 150
        });

        anime({
            targets: '.orbit-outer',
            ...baseAnimation,
            rotateY: [0, 180],
            duration: duration + 200,
            delay: 300
        });

        // Skill icons appear after orbits arrive
        anime({
            targets: skillIcons,
            scale: [0, 1],
            opacity: [0, 1],
            rotateX: [90, 0],
            duration: 400,
            delay: anime.stagger(40, {start: duration + 200}),
            easing: 'easeOutBack(1.7)'
        });
    }

    // Decoding animation for the name (optimized)
    function startDecoding() {
        if (!decodingOverlay || !decodedName) return;

        let iterations = 0;
        const maxIterations = config.reducedMotion ? 5 : 15; // Faster decoding
        let decodingSpeed = config.reducedMotion ? 200 : 60; // Faster initial speed

        // Show decoding overlay initially
        anime.set(decodingOverlay, { opacity: 1 });

        const decodingInterval = setInterval(() => {
            if (state.isSkipped || state.isCompleted) {
                clearInterval(decodingInterval);
                return;
            }

            let decodedText = '';

            // Progressive decoding - each character locks in place
            for (let i = 0; i < config.targetText.length; i++) {
                if (iterations > i * 1.2 + 3) { // Faster character reveal
                    decodedText += config.targetText[i];
                } else {
                    decodedText += config.characters[Math.floor(Math.random() * config.characters.length)];
                }
            }

            decodingOverlay.textContent = decodedText;
            iterations++;

            // Speed up decoding over time
            if (iterations > 8) {
                decodingSpeed = Math.max(30, decodingSpeed - 2);
            }

            if (iterations >= maxIterations) {
                clearInterval(decodingInterval);

                if (config.reducedMotion) {
                    // Simple fade for reduced motion
                    anime({
                        targets: decodingOverlay,
                        opacity: [1, 0],
                        duration: 300,
                        easing: 'easeOutQuad'
                    });
                } else {
                    // Final reveal with glitch effect
                    anime({
                        targets: decodingOverlay,
                        opacity: [1, 0, 1, 0],
                        duration: 200,
                        easing: 'steps(4)',
                        complete: function() {
                            // Smooth fade to reveal real name
                            anime({
                                targets: decodingOverlay,
                                opacity: [1, 0],
                                duration: 300,
                                easing: 'easeOutQuad'
                            });

                            // DISABLED: Pulse the decoded name to prevent conflicts with hero section
                            // anime({
                            //     targets: decodedName,
                            //     scale: [1, 1.05, 1],
                            //     textShadow: [
                            //         '0 0 10px rgba(0, 212, 255, 0.5)',
                            //         '0 0 15px rgba(0, 212, 255, 0.8)',
                            //         '0 0 10px rgba(0, 212, 255, 0.5)'
                            //     ],
                            //     duration: 400,
                            //     easing: 'easeOutElastic(1, .8)'
                            // });
                        }
                    });
                }
            }
        }, decodingSpeed);

        state.intervals.push(decodingInterval);
    }

    // Progress animation with real resource monitoring
    function startProgressAnimation() {
        let messageIndex = 0;
        let displayProgress = 0;

        const progressInterval = setInterval(() => {
            if (state.isSkipped || state.isCompleted) {
                clearInterval(progressInterval);
                return;
            }

            // Combine real resource loading with simulated progress
            const targetProgress = Math.min(
                (state.resourcesLoaded * 70) + (displayProgress * 0.3),
                100
            );

            // Smooth progress increment
            if (displayProgress < targetProgress) {
                displayProgress += Math.random() * 8 + 2;
            }

            if (displayProgress >= 100) {
                displayProgress = 100;
                clearInterval(progressInterval);
            }

            // Update progress bar with hardware acceleration
            if (progressFill) {
                anime({
                    targets: progressFill,
                    width: displayProgress + '%',
                    duration: 150,
                    easing: 'easeOutQuad'
                });
            }

            // Update percentage
            if (loadingPercentage) {
                loadingPercentage.textContent = Math.floor(displayProgress) + '%';
            }

            // Update loading message based on progress
            const newMessageIndex = Math.min(
                Math.floor((displayProgress / 100) * loadingMessages.length),
                loadingMessages.length - 1
            );

            if (newMessageIndex > messageIndex && loadingText) {
                messageIndex = newMessageIndex;
                loadingText.textContent = loadingMessages[messageIndex];

                // Update status based on progress
                if (displayProgress < 30) {
                    updateLoadingStatus('Loading assets...');
                } else if (displayProgress < 70) {
                    updateLoadingStatus('Processing data...');
                } else if (displayProgress < 90) {
                    updateLoadingStatus('Finalizing...');
                } else {
                    updateLoadingStatus('Ready!');
                }
            }

            state.progress = displayProgress;
        }, 100);

        state.intervals.push(progressInterval);
    }

    // Complete preloader and transition to main site
    function completePreloader() {
        if (state.isCompleted) return;
        state.isCompleted = true;

        // Cleanup all intervals and timeouts
        cleanup();

        // Final message with glow effect
        if (loadingText) {
            loadingText.textContent = 'LAUNCH COMPLETE!';
        }

        updateLoadingStatus('Complete!');

        if (config.reducedMotion) {
            // Simple fade out for reduced motion
            anime({
                targets: preloader,
                opacity: [1, 0],
                duration: 500,
                easing: 'easeOutQuad',
                complete: function() {
                    preloader.style.display = 'none';
                    initMainAnimations();
                }
            });
            return;
        }

        // Animate final elements
        anime({
            targets: [loadingText, loadingPercentage],
            color: ['#00d4ff', '#4ecdcc', '#00d4ff'],
            textShadow: [
                '0 0 5px rgba(0, 212, 255, 0.5)',
                '0 0 15px rgba(0, 212, 255, 0.8)',
                '0 0 5px rgba(0, 212, 255, 0.5)'
            ],
            duration: 600,
            easing: 'easeInOutSine'
        });

        // Smooth multi-stage exit with error handling
        const timeout1 = setTimeout(() => {
            // Stage 1: Fade out progress and text
            anime({
                targets: '.loading-progress-container',
                opacity: [1, 0],
                translateY: [0, 20],
                duration: 400,
                easing: 'easeInQuad'
            });

            // Stage 2: Shrink orbits
            const timeout2 = setTimeout(() => {
                anime({
                    targets: '.orbit',
                    scale: [1, 0],
                    opacity: [1, 0],
                    duration: 500,
                    delay: anime.stagger(50),
                    easing: 'easeInBack(1.7)'
                });

                anime({
                    targets: '.skill-icon',
                    scale: [1, 0],
                    opacity: [1, 0],
                    duration: 400,
                    delay: anime.stagger(30),
                    easing: 'easeInQuad'
                });
            }, 200);

            // Stage 3: DISABLED Final name pulse and fade to prevent conflicts
            const timeout3 = setTimeout(() => {
                // DISABLED: decodedName animation to prevent conflicts with hero section
                // if (decodedName) {
                //     anime({
                //         targets: decodedName,
                //         scale: [1, 1.2],
                //         opacity: [1, 0],
                //         textShadow: [
                //             '0 0 10px rgba(0, 212, 255, 0.5)',
                //             '0 0 30px rgba(0, 212, 255, 1)'
                //         ],
                //         duration: 600,
                //         easing: 'easeInQuad'
                //     });
                // }
            }, 500);

            // Stage 4: Final preloader fade
            const timeout4 = setTimeout(() => {
                anime({
                    targets: preloader,
                    opacity: [1, 0],
                    duration: 600,
                    easing: 'easeInOutQuad',
                    complete: function() {
                        preloader.style.display = 'none';
                        // Initialize main animations
                        initMainAnimations();
                    }
                });
            }, 1000);
        }, 400);
    }

    // Error handling
    function handleError(error) {
        console.warn('Preloader error:', error);
        preloader.classList.add('error');
        updateLoadingStatus('Error occurred, continuing...');

        // Continue with simplified completion
        const timeoutId = setTimeout(() => {
            completePreloader();
        }, 1000);
        state.timeouts.push(timeoutId);
    }

    // Initialize with error handling
    try {
        startPreloaderSequence();
    } catch (error) {
        handleError(error);
    }

    // Fallback timeout to ensure preloader never gets stuck
    const fallbackTimeout = setTimeout(() => {
        if (!state.isCompleted) {
            console.warn('Preloader fallback timeout triggered');
            completePreloader();
        }
    }, 5000); // 5 second maximum
    state.timeouts.push(fallbackTimeout);

    // Handle page visibility changes
    document.addEventListener('visibilitychange', () => {
        if (document.hidden && !state.isCompleted) {
            // Page is hidden, speed up completion
            state.isSkipped = true;
            completePreloader();
        }
    });

    // Handle window beforeunload
    window.addEventListener('beforeunload', () => {
        cleanup();
    });
}

// Enhanced initialization state tracking to prevent double animations
const AnimationManager = {
    initialized: false,
    initializationPromise: null,
    initializationSource: null,

    async initialize(source = 'unknown') {
        console.log('🚀 [ANIMATION MANAGER] ===== INITIALIZATION REQUEST =====');
        console.log('🚀 [ANIMATION MANAGER] Source:', source);
        console.log('🚀 [ANIMATION MANAGER] Current status:', this.initialized);
        console.log('🚀 [ANIMATION MANAGER] Timestamp:', new Date().toISOString());

        // If already initialized, return immediately
        if (this.initialized) {
            console.log('🚀 [ANIMATION MANAGER] ❌ Already initialized by:', this.initializationSource);
            return Promise.resolve();
        }

        // If initialization is in progress, return the existing promise
        if (this.initializationPromise) {
            console.log('🚀 [ANIMATION MANAGER] ⏳ Initialization in progress, waiting...');
            return this.initializationPromise;
        }

        // Start new initialization
        console.log('🚀 [ANIMATION MANAGER] ✅ Starting new initialization...');
        this.initializationSource = source;

        this.initializationPromise = this._performInitialization();

        try {
            await this.initializationPromise;
            this.initialized = true;
            console.log('🚀 [ANIMATION MANAGER] ✅ Initialization completed successfully');
        } catch (error) {
            console.error('🚀 [ANIMATION MANAGER] ❌ Initialization failed:', error);
            this.initializationPromise = null; // Allow retry
            throw error;
        }

        return this.initializationPromise;
    },

    async _performInitialization() {
        console.log('🚀 [ANIMATION MANAGER] 🎬 Performing initialization...');

        // Wait for DOM to be ready if needed
        if (document.readyState === 'loading') {
            await new Promise(resolve => {
                document.addEventListener('DOMContentLoaded', resolve, { once: true });
            });
        }

        // Initialize hero animations first with detailed logging
        console.log('🚀 [ANIMATION MANAGER] 📍 Step 1: Calling initHeroAnimations()');
        initHeroAnimations();

        console.log('🚀 [ANIMATION MANAGER] 📍 Step 2: Calling initTypingAnimation()');
        initTypingAnimation();

        console.log('🚀 [ANIMATION MANAGER] 📍 Step 3: Calling initGradientTextAnimation()');
        initGradientTextAnimation();

        console.log('🚀 [ANIMATION MANAGER] 📍 Step 4: Calling initMouseTrailParticles()');
        initMouseTrailParticles();

        console.log('🚀 [ANIMATION MANAGER] 📍 Step 5: Calling initWaveDistortion()');
        initWaveDistortion();

        // Initialize other sections
        console.log('🚀 [ANIMATION MANAGER] 📍 Step 6: Initializing other sections...');
        initCardAnimations();
        initCardHoverEffects();
        initSkillTagAnimations();
        initLanguageProgressAnimations();
        initTimelineAnimations();
        initEvolutionAnimations();
        initInteractiveEvolution();
        initNetworkAnimation();
        initPortfolioAnimations();
        initVideoModal();
        initPortfolioLinks();
        initTerminalContact();
        initTerminalAnimations();
        initTerminalTyping();

        console.log('🚀 [ANIMATION MANAGER] ✅ All animations initialized successfully');
    }
};

// Legacy function for backward compatibility
function initMainAnimations() {
    return AnimationManager.initialize('legacy-call');
}

// Listen for preloader completion
document.addEventListener('preloaderComplete', function(event) {
    console.log('🎯 [EVENT DEBUG] ===== PRELOADER COMPLETE EVENT FIRED =====');
    console.log('🎯 [EVENT DEBUG] Event detail:', event.detail);
    console.log('🎯 [EVENT DEBUG] Timestamp:', new Date().toISOString());
    console.log('🎯 [EVENT DEBUG] About to initialize animations via AnimationManager...');
    AnimationManager.initialize('preloader-complete');
});

// TESTING: Direct animation initialization
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎯 [EVENT DEBUG] ===== DOM CONTENT LOADED EVENT FIRED =====');
    console.log('🎯 [EVENT DEBUG] Timestamp:', new Date().toISOString());
    console.log('🎯 [EVENT DEBUG] Document readyState:', document.readyState);

    // Initialize EmailJS first
    console.log('🎯 [EVENT DEBUG] Initializing EmailJS...');
    initEmailJS();

    // TESTING: Start animations immediately without preloader
    console.log('🎯 [EVENT DEBUG] ⚠️ STARTING ANIMATIONS IMMEDIATELY FOR TESTING');
    setTimeout(() => {
        console.log('🎯 [EVENT DEBUG] ===== DIRECT ANIMATION INITIALIZATION =====');
        AnimationManager.initialize('direct-testing');
    }, 500); // Short delay to ensure DOM is ready
});

// Cleanup function to prevent memory leaks and animation conflicts
function cleanupAnimations() {
    console.log('🧹 [CLEANUP] Cleaning up animations...');

    // Stop all anime.js animations
    anime.running.forEach(animation => {
        if (animation.pause) animation.pause();
    });

    // Clear any timeouts and intervals
    const highestTimeoutId = setTimeout(() => {}, 0);
    for (let i = 0; i < highestTimeoutId; i++) {
        clearTimeout(i);
    }

    const highestIntervalId = setInterval(() => {}, 0);
    clearInterval(highestIntervalId);
    for (let i = 0; i < highestIntervalId; i++) {
        clearInterval(i);
    }

    // Reset initialization flags
    document.body.removeAttribute('data-hero-animations-initialized');
    document.body.removeAttribute('data-hero-animations-completed');
    document.body.removeAttribute('data-typing-animation-initialized');
    document.body.removeAttribute('data-gradient-text-initialized');
    document.body.removeAttribute('data-name-entrance-completed');
    document.body.removeAttribute('data-mouse-particles-initialized');
    document.body.removeAttribute('data-wave-distortion-initialized');

    // Reset AnimationManager
    AnimationManager.initialized = false;
    AnimationManager.initializationPromise = null;
    AnimationManager.initializationSource = null;

    console.log('🧹 [CLEANUP] Cleanup complete');
}

// Add cleanup listeners
window.addEventListener('beforeunload', cleanupAnimations);
window.addEventListener('pagehide', cleanupAnimations);

// For development: add a global cleanup function
window.cleanupAnimations = cleanupAnimations;
