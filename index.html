<!doctype html>
<html lang="en-US">
<head>
	<!-- Meta -->
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
	<meta name="description" content="<PERSON> Enthusiast, Web Developer & Creative Professional" />
	<meta name="keywords" content="alex castro, ai enthusiast, web developer, automation, virtual assistant, portfolio" />
	<meta name="author" content="<PERSON>" />

	<!-- Title -->
	<title><PERSON> - AI Enthusiast & Web Developer</title>

	<!-- Fonts -->
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
	<link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap" rel="stylesheet">

	<!-- Anime.js -->
	<script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>

	<!-- Font Awesome -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

	<!-- Custom Styles -->
	<link rel="stylesheet" href="css/modern-style.css" />

	<link rel="shortcut icon" href="images/favicons/favicon.ico">

	<!-- Service Worker Removed - Not needed for simple portfolio website -->
</head>

<body>
	<!-- Preloader -->
	<div class="preloader" id="preloader">
		<!-- Skip Button -->
		<button class="preloader-skip" id="skipPreloader" aria-label="Skip preloader animation">
			<i class="fas fa-forward"></i> Skip
		</button>

		<div class="preloader-content">
			<!-- Orbital Skill System with Central Name -->
			<div class="orbit-container" id="orbitContainer">
				<!-- Central Loading Text with Decoding Animation -->
				<div class="name-decoder">
					<div class="decoded-name" id="decodedName">LOADING</div>
					<div class="decoding-overlay" id="decodingOverlay"></div>
				</div>
				<!-- Inner Orbit -->
				<div class="orbit orbit-inner">
					<div class="skill-icon" data-skill="react">
						<i class="fab fa-react"></i>
					</div>
					<div class="skill-icon" data-skill="js">
						<i class="fab fa-js-square"></i>
					</div>
					<div class="skill-icon" data-skill="python">
						<i class="fab fa-python"></i>
					</div>
					<div class="skill-icon" data-skill="node">
						<i class="fab fa-node-js"></i>
					</div>
				</div>

				<!-- Middle Orbit -->
				<div class="orbit orbit-middle">
					<div class="skill-icon" data-skill="html">
						<i class="fab fa-html5"></i>
					</div>
					<div class="skill-icon" data-skill="css">
						<i class="fab fa-css3-alt"></i>
					</div>
					<div class="skill-icon" data-skill="git">
						<i class="fab fa-git-alt"></i>
					</div>
					<div class="skill-icon" data-skill="database">
						<i class="fas fa-database"></i>
					</div>
					<div class="skill-icon" data-skill="cloud">
						<i class="fas fa-cloud"></i>
					</div>
				</div>

				<!-- Outer Orbit -->
				<div class="orbit orbit-outer">
					<div class="skill-icon" data-skill="ai">
						<i class="fas fa-brain"></i>
					</div>
					<div class="skill-icon" data-skill="analytics">
						<i class="fas fa-chart-line"></i>
					</div>
					<div class="skill-icon" data-skill="automation">
						<i class="fas fa-robot"></i>
					</div>
					<div class="skill-icon" data-skill="design">
						<i class="fas fa-palette"></i>
					</div>
					<div class="skill-icon" data-skill="mobile">
						<i class="fas fa-mobile-alt"></i>
					</div>
					<div class="skill-icon" data-skill="api">
						<i class="fas fa-plug"></i>
					</div>
				</div>
			</div>

			<!-- Loading Progress -->
			<div class="loading-progress-container">
				<div class="progress-bar">
					<div class="progress-fill" id="progressFill"></div>
				</div>
				<div class="loading-percentage" id="loadingPercentage">0%</div>
			</div>
		</div>

		<!-- Background Effects -->
		<div class="preloader-background">
			<div class="grid-overlay"></div>
			<div class="scan-lines"></div>
		</div>
	</div>

	<!-- Navigation -->
	<nav class="navbar" id="navbar">
		<div class="nav-container">
			<div class="nav-logo">
				<span class="logo-ac">AC</span>
				<span class="logo-name">Alex Castro</span>
			</div>

			<div class="nav-menu" id="nav-menu">
				<a href="#hero" class="nav-link active">Home</a>
				<a href="#about" class="nav-link">About</a>
				<a href="#journey" class="nav-link">Evolution</a>
				<a href="#portfolio" class="nav-link">Portfolio</a>
				<a href="#contact" class="nav-link">Contact</a>
			</div>

			<div class="nav-actions">
				<a href="#contact" class="btn-primary">Get In Touch</a>
				<div class="hamburger" id="hamburger">
					<span></span>
					<span></span>
					<span></span>
				</div>
			</div>
		</div>
	</nav>

	<!-- Hero Section -->
	<section class="hero" id="hero">
		<div class="hero-background">
			<div class="particles-container" id="particles"></div>
			<div class="gradient-overlay"></div>
			<!-- Mouse Trail Particles -->
			<canvas id="particleCanvas" class="particle-canvas"></canvas>
			<!-- Wave Distortion Effect -->
			<div class="wave-container">
				<svg class="wave-svg" viewBox="0 0 1200 600" preserveAspectRatio="none">
					<defs>
						<linearGradient id="waveGradient" x1="0%" y1="0%" x2="100%" y2="100%">
							<stop offset="0%" style="stop-color:rgba(0,212,255,0.15);stop-opacity:1" />
							<stop offset="50%" style="stop-color:rgba(78,205,196,0.2);stop-opacity:1" />
							<stop offset="100%" style="stop-color:rgba(0,212,255,0.1);stop-opacity:1" />
						</linearGradient>
					</defs>
					<path class="wave" d="M0,350 Q300,280 600,350 T1200,350 L1200,600 L0,600 Z" fill="url(#waveGradient)"></path>
				</svg>
			</div>
		</div>

		<div class="hero-content">
			<div class="hero-text">
				<div class="hero-greeting">
					<span class="greeting-text">Hello, I'm</span>
				</div>

				<h1 class="hero-name">
					<span class="name-first gradient-text">Alex</span>
					<span class="name-last gradient-text">Castro</span>
				</h1>

				<div class="hero-title">
					<span class="title-prefix">I'm a</span>
					<span class="title-dynamic" id="dynamic-title"></span>
					<span class="cursor">|</span>
				</div>

				<p class="hero-description">
					AI Enthusiast and Creative Professional passionate about automation,
					web development, and innovative solutions. Let's build something amazing together.
				</p>

				<div class="hero-actions">
					<a href="#portfolio" class="btn-primary">
						<span>View My Work</span>
						<i class="fas fa-arrow-right"></i>
					</a>
					<a href="#contact" class="btn-secondary">
						<span>Let's Talk</span>
						<i class="fas fa-comments"></i>
					</a>
				</div>

				<div class="hero-social">
					<a href="https://www.linkedin.com/in/alex-castro-422b39261" target="_blank" class="social-link" aria-label="LinkedIn">
						<i class="fab fa-linkedin-in"></i>
					</a>
					<a href="https://wa.me/573012372817" target="_blank" class="social-link" aria-label="WhatsApp">
						<i class="fab fa-whatsapp"></i>
					</a>
					<a href="mailto:<EMAIL>" class="social-link" aria-label="Email">
						<i class="fas fa-envelope"></i>
					</a>
				</div>
			</div>

			<div class="hero-visual">
				<div class="profile-container">
					<div class="profile-image profile-glitch">
						<img src="images/man.png" alt="Alex Castro" />
						<div class="profile-glow"></div>
					</div>
					<div class="floating-elements">
						<div class="floating-icon" data-icon="brain">
							<i class="fas fa-brain"></i>
						</div>
						<div class="floating-icon" data-icon="code">
							<i class="fas fa-code"></i>
						</div>
						<div class="floating-icon" data-icon="robot">
							<i class="fas fa-robot"></i>
						</div>
						<div class="floating-icon" data-icon="palette">
							<i class="fas fa-palette"></i>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="scroll-indicator">
			<div class="scroll-text">Scroll Down</div>
			<div class="scroll-arrow">
				<i class="fas fa-chevron-down"></i>
			</div>
		</div>
	</section>

		<!-- About Section -->
		<section class="section-container" id="about">
			<div class="section-content">
				<div class="section-header">
					<h2 class="section-title">About Me</h2>
					<p class="section-subtitle">Discover my journey and expertise</p>
				</div>

				<div class="cards-grid">
					<!-- Personal Info Card -->
					<div class="neon-card" data-card="personal">
						<div class="card-header">
							<div class="card-icon">
								<i class="fas fa-user"></i>
							</div>
							<h3 class="card-title">Personal Info</h3>
						</div>
						<div class="card-content">
							<div class="scrollable-content">
								<div class="info-grid">
									<div class="info-item">
										<span class="info-label">Name</span>
										<span class="info-value">Alex Castro</span>
									</div>
									<div class="info-item">
										<span class="info-label">Age</span>
										<span class="info-value">45</span>
									</div>
									<div class="info-item">
										<span class="info-label">Location</span>
										<span class="info-value">Medellín, Colombia</span>
									</div>
									<div class="info-item">
										<span class="info-label">Email</span>
										<span class="info-value"><EMAIL></span>
									</div>
									<div class="info-item">
										<span class="info-label">Phone</span>
										<span class="info-value">+57 (*************</span>
									</div>
									<div class="info-item">
										<span class="info-label">Status</span>
										<span class="info-value status-available">Available for Freelance</span>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- Professional Journey Card -->
					<div class="neon-card" data-card="journey">
						<div class="card-header">
							<div class="card-icon">
								<i class="fas fa-rocket"></i>
							</div>
							<h3 class="card-title">My Journey</h3>
						</div>
						<div class="card-content">
							<div class="scrollable-content">
								<p class="journey-text">
									Hi there! I'm a versatile professional and <strong>Artificial Intelligence enthusiast</strong> with a passion for technology and creative problem-solving. My journey has taken me through various roles in customer support, sales, and business development, where I've honed my ability to connect with people and drive results.
								</p>
								<p class="journey-text">
									I'm also a creative at heart, with hands-on experience in <strong>graphic design, motion design, and video editing</strong>. Recently, I've been diving into web development and working with generative AIs, including Claude Pro and ChatGPT Plus, achieving great success in leveraging these cutting-edge technologies.
								</p>
								<p class="journey-text">
									As a lifelong learner, I'm always seeking new challenges. I'm particularly excited about <strong>automation and its potential</strong> to streamline processes. Currently, I'm exploring opportunities in the Virtual Assistant space, where I can apply my diverse skills and AI expertise to support busy professionals and growing businesses.
								</p>
							</div>
						</div>
					</div>

					<!-- Skills & Expertise Card -->
					<div class="neon-card" data-card="skills">
						<div class="card-header">
							<div class="card-icon">
								<i class="fas fa-brain"></i>
							</div>
							<h3 class="card-title">Skills & Expertise</h3>
						</div>
						<div class="card-content">
							<div class="scrollable-content">
								<div class="skills-category">
									<h4 class="category-title">Technical Skills</h4>
									<div class="skill-tags">
										<span class="skill-tag">HTML/CSS/JavaScript</span>
										<span class="skill-tag">Python</span>
										<span class="skill-tag">Web Development</span>
										<span class="skill-tag">Automation</span>
										<span class="skill-tag">AI Tools</span>
									</div>
								</div>
								<div class="skills-category">
									<h4 class="category-title">Creative Skills</h4>
									<div class="skill-tags">
										<span class="skill-tag">Graphic Design</span>
										<span class="skill-tag">Motion Design</span>
										<span class="skill-tag">Video Editing</span>
										<span class="skill-tag">Adobe Creative Suite</span>
									</div>
								</div>
								<div class="skills-category">
									<h4 class="category-title">Business Skills</h4>
									<div class="skill-tags">
										<span class="skill-tag">Customer Support</span>
										<span class="skill-tag">Sales</span>
										<span class="skill-tag">Project Management</span>
										<span class="skill-tag">Virtual Assistance</span>
									</div>
								</div>
								<div class="skills-category">
									<h4 class="category-title">Languages</h4>
									<div class="language-skills">
										<div class="language-item">
											<span class="language-name">Spanish</span>
											<div class="language-bar">
												<div class="language-progress" style="width: 100%"></div>
											</div>
											<span class="language-level">Native</span>
										</div>
										<div class="language-item">
											<span class="language-name">English</span>
											<div class="language-bar">
												<div class="language-progress" style="width: 90%"></div>
											</div>
											<span class="language-level">C1 - Fluent</span>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>


				</div>
			</div>
		</section>

		<!-- Work Journey Section -->
		<section class="section-container" id="journey">
			<div class="section-content">
				<div class="section-header">
					<h2 class="section-title">Professional Evolution</h2>
					<p class="section-subtitle">Navigate through my career transformation</p>
				</div>

				<div class="evolution-container">
					<!-- Background Network -->
					<div class="network-background">
						<svg class="network-svg" viewBox="0 0 1400 800" preserveAspectRatio="xMidYMid meet">
							<defs>
								<linearGradient id="networkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
									<stop offset="0%" style="stop-color:rgba(0, 212, 255, 0.1);stop-opacity:1" />
									<stop offset="50%" style="stop-color:rgba(78, 205, 196, 0.2);stop-opacity:1" />
									<stop offset="100%" style="stop-color:rgba(255, 107, 107, 0.1);stop-opacity:1" />
								</linearGradient>
								<filter id="networkGlow">
									<feGaussianBlur stdDeviation="2" result="coloredBlur"/>
									<feMerge>
										<feMergeNode in="coloredBlur"/>
										<feMergeNode in="SourceGraphic"/>
									</feMerge>
								</filter>
							</defs>
							<!-- Connection Lines -->
							<path class="connection-line line-1" d="M 200 150 Q 400 100 600 200" stroke="url(#networkGradient)" stroke-width="2" fill="none" filter="url(#networkGlow)"/>
							<path class="connection-line line-2" d="M 600 200 Q 800 300 1000 250" stroke="url(#networkGradient)" stroke-width="2" fill="none" filter="url(#networkGlow)"/>
							<path class="connection-line line-3" d="M 1000 250 Q 1100 150 1200 200" stroke="url(#networkGradient)" stroke-width="2" fill="none" filter="url(#networkGlow)"/>
							<path class="connection-line line-4" d="M 200 150 Q 300 400 600 500" stroke="url(#networkGradient)" stroke-width="2" fill="none" filter="url(#networkGlow)"/>
							<path class="connection-line line-5" d="M 600 500 Q 900 450 1200 400" stroke="url(#networkGradient)" stroke-width="2" fill="none" filter="url(#networkGlow)"/>
							<!-- Data Particles -->
							<circle class="data-particle particle-1" cx="200" cy="150" r="3" fill="#00d4ff"/>
							<circle class="data-particle particle-2" cx="600" cy="200" r="3" fill="#4ecdc4"/>
							<circle class="data-particle particle-3" cx="1000" cy="250" r="3" fill="#ff6b6b"/>
							<circle class="data-particle particle-4" cx="600" cy="500" r="3" fill="#00d4ff"/>
							<circle class="data-particle particle-5" cx="1200" cy="400" r="3" fill="#4ecdc4"/>
						</svg>
					</div>

					<!-- Evolution Stages -->
					<div class="evolution-stages">
						<!-- Stage 1: Foundation -->
						<div class="evolution-stage" data-stage="1" data-year="2014-2019">
							<div class="stage-container">
								<div class="stage-header">
									<div class="stage-icon">
										<i class="fas fa-tools"></i>
									</div>
									<div class="stage-meta">
										<span class="stage-year">2014-2019</span>
										<h3 class="stage-title">Foundation Era</h3>
									</div>
									<div class="expand-button">
										<i class="fas fa-chevron-down"></i>
									</div>
								</div>
								<div class="stage-content">
									<div class="company-card">
										<div class="company-logo">XH</div>
										<div class="company-info">
											<h4>Xtreme Hardware Lab</h4>
											<p>Computer Technician → Lab Manager</p>
										</div>
									</div>
									<div class="evolution-details">
										<div class="skill-evolution">
											<div class="skill-node" data-skill="hardware">
												<span class="skill-name">Hardware Mastery</span>
												<div class="skill-connections"></div>
											</div>
											<div class="skill-node" data-skill="recovery">
												<span class="skill-name">Data Recovery</span>
												<div class="skill-connections"></div>
											</div>
											<div class="skill-node" data-skill="leadership">
												<span class="skill-name">Team Leadership</span>
												<div class="skill-connections"></div>
											</div>
										</div>
										<div class="transformation-arrow">
											<div class="arrow-body"></div>
											<div class="arrow-head"></div>
											<span class="transformation-text">Technical Foundation Built</span>
										</div>
									</div>
									<div class="expanded-content">
										<div class="detailed-description">
											<h4>Building the Technical Foundation</h4>
											<p>Started as a computer technician at Xtreme Hardware, performing hardware and software repairs on various devices. Due to exceptional performance and technical expertise, I was promoted to manager of Xtreme LAB.</p>

											<h5>Key Responsibilities:</h5>
											<ul>
												<li><strong>Hardware Diagnostics:</strong> Expert-level troubleshooting of computer hardware issues</li>
												<li><strong>Software Repair:</strong> Comprehensive software problem resolution and system optimization</li>
												<li><strong>Data Recovery Specialist:</strong> Advanced data recovery techniques for critical client information</li>
												<li><strong>Team Management:</strong> Led the Xtreme LAB team and managed daily operations</li>
												<li><strong>Customer Relations:</strong> Provided excellent customer service and technical support</li>
											</ul>

											<h5>Skills Developed:</h5>
											<p>This foundational period established my technical expertise, problem-solving abilities, and leadership skills. The experience in data recovery and hardware mastery became the cornerstone of my technical career.</p>
										</div>
									</div>
								</div>
							</div>
						</div>

						<!-- Stage 2: Transition -->
						<div class="evolution-stage" data-stage="2" data-year="2022-2023">
							<div class="stage-container">
								<div class="stage-header">
									<div class="stage-icon">
										<i class="fas fa-bullhorn"></i>
									</div>
									<div class="stage-meta">
										<span class="stage-year">2022-2023</span>
										<h3 class="stage-title">Market Expansion</h3>
									</div>
									<div class="expand-button">
										<i class="fas fa-chevron-down"></i>
									</div>
								</div>
								<div class="stage-content">
									<div class="company-card">
										<div class="company-logo">BS</div>
										<div class="company-info">
											<h4>BuenaSuerte Advertising</h4>
											<p>Sales Agent → Lead Generator</p>
										</div>
									</div>
									<div class="evolution-details">
										<div class="skill-evolution">
											<div class="skill-node" data-skill="sales">
												<span class="skill-name">Sales Mastery</span>
												<div class="skill-connections"></div>
											</div>
											<div class="skill-node" data-skill="crm">
												<span class="skill-name">CRM Systems</span>
												<div class="skill-connections"></div>
											</div>
											<div class="skill-node" data-skill="marketing">
												<span class="skill-name">Digital Marketing</span>
												<div class="skill-connections"></div>
											</div>
										</div>
										<div class="transformation-arrow">
											<div class="arrow-body"></div>
											<div class="arrow-head"></div>
											<span class="transformation-text">Business Acumen Developed</span>
										</div>
									</div>
									<div class="expanded-content">
										<div class="detailed-description">
											<h4>Expanding into Sales & Marketing</h4>
											<p>Worked as a call agent selling print and online advertising in Houston, Texas. This role marked my transition from technical work to business development and sales.</p>

											<h5>Core Activities:</h5>
											<ul>
												<li><strong>Print & Digital Advertising Sales:</strong> Successfully sold advertising solutions to businesses across Houston</li>
												<li><strong>CRM Management:</strong> Utilized company CRM systems for effective lead management and client tracking</li>
												<li><strong>Lead Generation:</strong> Learned and implemented lead scraping techniques to acquire new clients</li>
												<li><strong>Client Prospecting:</strong> Developed skills in identifying and qualifying potential advertising clients</li>
												<li><strong>Sales Process Optimization:</strong> Gained experience in both traditional and digital advertising sales</li>
											</ul>

											<h5>Key Learning:</h5>
											<p>This experience taught me the fundamentals of sales, customer relationship management, and digital marketing strategies. It was crucial in developing my business acumen and understanding of market dynamics.</p>
										</div>
									</div>
								</div>
							</div>
						</div>

						<!-- Stage 3: Innovation -->
						<div class="evolution-stage" data-stage="3" data-year="2023-2024">
							<div class="stage-container">
								<div class="stage-header">
									<div class="stage-icon">
										<i class="fas fa-rocket"></i>
									</div>
									<div class="stage-meta">
										<span class="stage-year">2023-2024</span>
										<h3 class="stage-title">Innovation Phase</h3>
									</div>
									<div class="expand-button">
										<i class="fas fa-chevron-down"></i>
									</div>
								</div>
								<div class="stage-content">
									<div class="company-card">
										<div class="company-logo">WB</div>
										<div class="company-info">
											<h4>Worldwide BDC</h4>
											<p>BDC Rep → System Developer</p>
										</div>
									</div>
									<div class="evolution-details">
										<div class="skill-evolution">
											<div class="skill-node" data-skill="optimization">
												<span class="skill-name">Process Optimization</span>
												<div class="skill-connections"></div>
											</div>
											<div class="skill-node" data-skill="development">
												<span class="skill-name">Tool Development</span>
												<div class="skill-connections"></div>
											</div>
											<div class="skill-node" data-skill="analytics">
												<span class="skill-name">Performance Analytics</span>
												<div class="skill-connections"></div>
											</div>
										</div>
										<div class="transformation-arrow">
											<div class="arrow-body"></div>
											<div class="arrow-head"></div>
											<span class="transformation-text">Innovation Mindset Unlocked</span>
										</div>
									</div>
									<div class="expanded-content">
										<div class="detailed-description">
											<h4>The Innovation Breakthrough</h4>
											<p>Started as a BDC representative but quickly identified inefficiencies in VinSolutions and eLeads CRM systems. This led to developing personal tools that enhanced performance, ultimately resulting in a role transition to implement these systems company-wide.</p>

											<h5>Initial Role & Discovery:</h5>
											<ul>
												<li><strong>BDC Operations:</strong> Handled customer inquiries and lead management as a BDC representative</li>
												<li><strong>System Analysis:</strong> Identified critical inefficiencies in existing CRM workflows</li>
												<li><strong>Problem-Solving Initiative:</strong> Took initiative to develop personal productivity tools</li>
											</ul>

											<h5>Innovation & Development:</h5>
											<ul>
												<li><strong>Custom Tool Creation:</strong> Developed tools to enhance personal and team performance</li>
												<li><strong>Security Implementation:</strong> Added security measures to protect company data</li>
												<li><strong>Performance Reporting:</strong> Created comprehensive reporting systems for all agents</li>
												<li><strong>Company-wide Implementation:</strong> Successfully rolled out solutions across the organization</li>
											</ul>

											<h5>Impact:</h5>
											<p>This role marked my transition from user to creator, significantly enhancing overall operational effectiveness and establishing me as an innovative problem-solver within the organization.</p>
										</div>
									</div>
								</div>
							</div>
						</div>

						<!-- Stage 4: AI Revolution -->
						<div class="evolution-stage" data-stage="4" data-year="2024">
							<div class="stage-container">
								<div class="stage-header">
									<div class="stage-icon">
										<i class="fas fa-brain"></i>
									</div>
									<div class="stage-meta">
										<span class="stage-year">2024</span>
										<h3 class="stage-title">AI Revolution</h3>
									</div>
									<div class="expand-button">
										<i class="fas fa-chevron-down"></i>
									</div>
								</div>
								<div class="stage-content">
									<div class="company-card">
										<div class="company-logo">TB</div>
										<div class="company-info">
											<h4>TrueBDC</h4>
											<p>Developer → AI Specialist</p>
										</div>
									</div>
									<div class="evolution-details">
										<div class="skill-evolution">
											<div class="skill-node" data-skill="ai">
												<span class="skill-name">AI Integration</span>
												<div class="skill-connections"></div>
											</div>
											<div class="skill-node" data-skill="automation">
												<span class="skill-name">Automation</span>
												<div class="skill-connections"></div>
											</div>
											<div class="skill-node" data-skill="realtime">
												<span class="skill-name">Real-time Systems</span>
												<div class="skill-connections"></div>
											</div>
										</div>
										<div class="transformation-arrow">
											<div class="arrow-body"></div>
											<div class="arrow-head"></div>
											<span class="transformation-text">Innovation Continues</span>
										</div>
									</div>
									<div class="expanded-content">
										<div class="detailed-description">
											<h4>Role Evolution & Achievements</h4>
											<p>Working as a developer at TrueBDC, I focused on creating tools to enhance agent efficiency and productivity. This role marked my transition into AI and automation technologies.</p>

											<h5>Key Developments:</h5>
											<ul>
												<li><strong>Automation Scripts:</strong> Developed scripts for automating repetitive processes, significantly reducing manual work</li>
												<li><strong>Click-to-Dial Functionality:</strong> Implemented seamless communication tools for agents</li>
												<li><strong>Fast-Refresh Systems:</strong> Created systems for real-time lead updates and management</li>
												<li><strong>Batch Processing Tools:</strong> Designed tools for handling multiple leads efficiently</li>
												<li><strong>Visual Error Prevention:</strong> Implemented visual aids to minimize operational errors</li>
												<li><strong>Real-time Reporting:</strong> Developed comprehensive reporting systems for all dealership clients</li>
											</ul>

											<h5>Impact:</h5>
											<p>These innovations significantly improved overall company productivity and established me as a key contributor to the organization's technological advancement.</p>
										</div>
									</div>
								</div>
							</div>
						</div>

						<!-- Stage 5: B2B Sales Excellence -->
						<div class="evolution-stage" data-stage="5" data-year="2024-Present">
							<div class="stage-container">
								<div class="stage-header">
									<div class="stage-icon">
										<i class="fas fa-chart-line"></i>
									</div>
									<div class="stage-meta">
										<span class="stage-year">2024-Present</span>
										<h3 class="stage-title">B2B Sales Excellence</h3>
									</div>
									<div class="expand-button">
										<i class="fas fa-chevron-down"></i>
									</div>
								</div>
								<div class="stage-content">
									<div class="company-card">
										<div class="company-logo">RL</div>
										<div class="company-info">
											<h4>Repair Lift</h4>
											<p>Business Development Representative (B2B)</p>
										</div>
									</div>
									<div class="evolution-details">
										<div class="skill-evolution">
											<div class="skill-node" data-skill="b2b">
												<span class="skill-name">B2B Sales</span>
												<div class="skill-connections"></div>
											</div>
											<div class="skill-node" data-skill="dashboards">
												<span class="skill-name">Dashboard Development</span>
												<div class="skill-connections"></div>
											</div>
											<div class="skill-node" data-skill="metrics">
												<span class="skill-name">Metrics Analysis</span>
												<div class="skill-connections"></div>
											</div>
											<div class="skill-node" data-skill="tools">
												<span class="skill-name">Custom Tools</span>
												<div class="skill-connections"></div>
											</div>
										</div>
										<div class="transformation-arrow">
											<div class="arrow-body"></div>
											<div class="arrow-head"></div>
											<span class="transformation-text">Excellence Through Innovation</span>
										</div>
									</div>
									<div class="expanded-content">
										<div class="detailed-description">
											<h4>B2B Marketing & Development Excellence</h4>
											<p>Currently serving as a Business Development Representative at Repair Lift, a B2B marketing company, where I combine sales expertise with technical innovation to drive exceptional results.</p>

											<h5>Core Responsibilities:</h5>
											<ul>
												<li><strong>B2B Lead Generation:</strong> Identifying and qualifying high-value business prospects</li>
												<li><strong>Client Relationship Management:</strong> Building and maintaining strategic B2B partnerships</li>
												<li><strong>Sales Process Optimization:</strong> Continuously improving conversion rates and efficiency</li>
												<li><strong>Market Research:</strong> Analyzing B2B market trends and opportunities</li>
											</ul>

											<h5>Technical Innovations:</h5>
											<ul>
												<li><strong>Custom Dashboards:</strong> Developed comprehensive dashboards to track and understand key performance metrics</li>
												<li><strong>Analytics Tools:</strong> Created tools for deep-dive analysis of sales performance and market trends</li>
												<li><strong>Automation Solutions:</strong> Built custom tools to streamline my workflow and maximize productivity</li>
												<li><strong>Performance Optimization:</strong> Implemented data-driven approaches to continuously improve my sales effectiveness</li>
											</ul>

											<h5>Results & Impact:</h5>
											<p>By combining traditional B2B sales skills with custom-built analytical tools and dashboards, I've been able to significantly outperform standard metrics and provide valuable insights to the marketing team.</p>
										</div>
									</div>
								</div>
							</div>
						</div>

						<!-- Future Stage -->
						<div class="evolution-stage future-stage" data-stage="6" data-year="Future">
							<div class="stage-container">
								<div class="stage-header">
									<div class="stage-icon">
										<i class="fas fa-star"></i>
									</div>
									<div class="stage-meta">
										<span class="stage-year">What's Next</span>
										<h3 class="stage-title">Your Project</h3>
									</div>
									<div class="expand-button">
										<i class="fas fa-chevron-down"></i>
									</div>
								</div>
								<div class="stage-content">
									<div class="company-card future-card">
										<div class="company-logo">?</div>
										<div class="company-info">
											<h4>Your Company</h4>
											<p>Let's Build Something Amazing</p>
										</div>
									</div>
									<div class="evolution-details">
										<div class="skill-evolution">
											<div class="skill-node" data-skill="innovation">
												<span class="skill-name">Innovation</span>
												<div class="skill-connections"></div>
											</div>
											<div class="skill-node" data-skill="collaboration">
												<span class="skill-name">Collaboration</span>
												<div class="skill-connections"></div>
											</div>
											<div class="skill-node" data-skill="solutions">
												<span class="skill-name">AI Solutions</span>
												<div class="skill-connections"></div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- Evolution Navigator -->
					<div class="evolution-navigator">
						<div class="nav-track">
							<div class="nav-progress"></div>
						</div>
						<div class="nav-dots">
							<div class="nav-dot active" data-stage="1"></div>
							<div class="nav-dot" data-stage="2"></div>
							<div class="nav-dot" data-stage="3"></div>
							<div class="nav-dot" data-stage="4"></div>
							<div class="nav-dot" data-stage="5"></div>
							<div class="nav-dot future-dot" data-stage="6"></div>
						</div>
					</div>
				</div>
			</div>
		</section>

		<!-- Portfolio Section -->
		<section class="section-container" id="portfolio">
			<div class="section-content">
				<div class="section-header">
					<h2 class="section-title">Portfolio Showcase</h2>
					<p class="section-subtitle">Explore my creative work and technical projects</p>
				</div>

				<div class="portfolio-container">
					<!-- Featured Videos Grid -->
					<div class="featured-videos">
						<h3 class="portfolio-section-title">
							<i class="fas fa-play-circle"></i>
							Featured Video Projects
						</h3>

						<div class="videos-grid">
							<!-- Video 1 -->
							<div class="video-card" data-video="1">
								<div class="video-container">
									<div class="video-placeholder">
										<div class="video-thumbnail">
											<div class="play-button">
												<i class="fas fa-play"></i>
											</div>
											<div class="video-overlay">
												<h4 class="video-title">Project Demo 1</h4>
												<p class="video-description">Dashboard Analytics Tool</p>
											</div>
										</div>
									</div>
									<div class="video-info">
										<div class="video-meta">
											<span class="video-category">Development</span>
											<span class="video-duration">3:45</span>
										</div>
										<h4 class="video-name">Custom Dashboard Creation</h4>
										<p class="video-details">Showcasing the development of a comprehensive analytics dashboard for performance tracking.</p>
									</div>
								</div>
							</div>

							<!-- Video 2 -->
							<div class="video-card" data-video="2">
								<div class="video-container">
									<div class="video-placeholder">
										<div class="video-thumbnail">
											<div class="play-button">
												<i class="fas fa-play"></i>
											</div>
											<div class="video-overlay">
												<h4 class="video-title">Project Demo 2</h4>
												<p class="video-description">Automation Script</p>
											</div>
										</div>
									</div>
									<div class="video-info">
										<div class="video-meta">
											<span class="video-category">Automation</span>
											<span class="video-duration">2:30</span>
										</div>
										<h4 class="video-name">Process Automation Tool</h4>
										<p class="video-details">Demonstration of automated workflow solutions that improved efficiency by 300%.</p>
									</div>
								</div>
							</div>

							<!-- Video 3 -->
							<div class="video-card" data-video="3">
								<div class="video-container">
									<div class="video-placeholder">
										<div class="video-thumbnail">
											<div class="play-button">
												<i class="fas fa-play"></i>
											</div>
											<div class="video-overlay">
												<h4 class="video-title">Project Demo 3</h4>
												<p class="video-description">CRM Integration</p>
											</div>
										</div>
									</div>
									<div class="video-info">
										<div class="video-meta">
											<span class="video-category">Integration</span>
											<span class="video-duration">4:15</span>
										</div>
										<h4 class="video-name">CRM System Enhancement</h4>
										<p class="video-details">Custom tools developed to enhance CRM functionality and user experience.</p>
									</div>
								</div>
							</div>

							<!-- Video 4 -->
							<div class="video-card" data-video="4">
								<div class="video-container">
									<div class="video-placeholder">
										<div class="video-thumbnail">
											<div class="play-button">
												<i class="fas fa-play"></i>
											</div>
											<div class="video-overlay">
												<h4 class="video-title">Project Demo 4</h4>
												<p class="video-description">AI Implementation</p>
											</div>
										</div>
									</div>
									<div class="video-info">
										<div class="video-meta">
											<span class="video-category">AI/ML</span>
											<span class="video-duration">5:20</span>
										</div>
										<h4 class="video-name">AI-Powered Solutions</h4>
										<p class="video-details">Implementation of AI tools for enhanced decision-making and automation.</p>
									</div>
								</div>
							</div>

							<!-- Video 5 -->
							<div class="video-card" data-video="5">
								<div class="video-container">
									<div class="video-placeholder">
										<div class="video-thumbnail">
											<div class="play-button">
												<i class="fas fa-play"></i>
											</div>
											<div class="video-overlay">
												<h4 class="video-title">Marketing Campaign 1</h4>
												<p class="video-description">B2B Lead Generation</p>
											</div>
										</div>
									</div>
									<div class="video-info">
										<div class="video-meta">
											<span class="video-category">Marketing</span>
											<span class="video-duration">3:15</span>
										</div>
										<h4 class="video-name">B2B Marketing Campaign</h4>
										<p class="video-details">Strategic marketing campaign showcasing lead generation and client acquisition techniques.</p>
									</div>
								</div>
							</div>

							<!-- Video 6 -->
							<div class="video-card" data-video="6">
								<div class="video-container">
									<div class="video-placeholder">
										<div class="video-thumbnail">
											<div class="play-button">
												<i class="fas fa-play"></i>
											</div>
											<div class="video-overlay">
												<h4 class="video-title">Marketing Campaign 2</h4>
												<p class="video-description">Digital Strategy</p>
											</div>
										</div>
									</div>
									<div class="video-info">
										<div class="video-meta">
											<span class="video-category">Marketing</span>
											<span class="video-duration">4:30</span>
										</div>
										<h4 class="video-name">Digital Marketing Strategy</h4>
										<p class="video-details">Comprehensive digital marketing approach with analytics-driven campaign optimization.</p>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- Portfolio Links -->
					<div class="portfolio-links">
						<h3 class="portfolio-section-title">
							<i class="fas fa-folder-open"></i>
							Complete Portfolio
						</h3>

						<div class="links-container">
							<!-- Google Drive Link -->
							<div class="portfolio-link-card main-portfolio">
								<div class="link-icon">
									<i class="fab fa-google-drive"></i>
								</div>
								<div class="link-content">
									<h4 class="link-title">Complete Portfolio Archive</h4>
									<p class="link-description">Access my full collection of projects, case studies, and creative work</p>
									<div class="link-stats">
										<span class="stat-item">
											<i class="fas fa-file-video"></i>
											25+ Videos
										</span>
										<span class="stat-item">
											<i class="fas fa-images"></i>
											50+ Projects
										</span>
										<span class="stat-item">
											<i class="fas fa-download"></i>
											Resources
										</span>
									</div>
								</div>
								<div class="link-action">
									<span class="action-text">View Portfolio</span>
									<i class="fas fa-external-link-alt"></i>
								</div>
							</div>

							<!-- Additional Links -->
							<div class="additional-links">
								<div class="portfolio-link-card">
									<div class="link-icon">
										<i class="fab fa-whatsapp"></i>
									</div>
									<div class="link-content">
										<h4 class="link-title">WhatsApp Contact</h4>
										<p class="link-description">Direct messaging for quick communication</p>
									</div>
									<div class="link-action">
										<i class="fas fa-external-link-alt"></i>
									</div>
								</div>

								<div class="portfolio-link-card">
									<div class="link-icon">
										<i class="fab fa-linkedin"></i>
									</div>
									<div class="link-content">
										<h4 class="link-title">LinkedIn Profile</h4>
										<p class="link-description">Professional network and career updates</p>
									</div>
									<div class="link-action">
										<i class="fas fa-external-link-alt"></i>
									</div>
								</div>

								<div class="portfolio-link-card">
									<div class="link-icon">
										<i class="fas fa-blog"></i>
									</div>
									<div class="link-content">
										<h4 class="link-title">Technical Blog</h4>
										<p class="link-description">Tutorials and technical documentation</p>
									</div>
									<div class="link-action">
										<i class="fas fa-external-link-alt"></i>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Video Modal -->
				<div class="video-modal" id="videoModal">
					<div class="modal-backdrop"></div>
					<div class="modal-content">
						<div class="modal-header">
							<h3 class="modal-title">Project Showcase</h3>
							<button class="modal-close">
								<i class="fas fa-times"></i>
							</button>
						</div>
						<div class="modal-body">
							<div class="video-player-container">
								<iframe class="video-player" src="" frameborder="0" allowfullscreen></iframe>
							</div>
							<div class="video-modal-info">
								<h4 class="modal-video-title"></h4>
								<p class="modal-video-description"></p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>

		<!-- Contact Section -->
		<section class="section-container" id="contact">
			<div class="section-content">
				<div class="section-header">
					<h2 class="section-title">Get In Touch</h2>
					<p class="section-subtitle">Initialize connection protocol</p>
				</div>

				<div class="terminal-container">
					<div class="terminal-window">
						<!-- Terminal Header -->
						<div class="terminal-header">
							<div class="terminal-controls">
								<div class="control-button close"></div>
								<div class="control-button minimize"></div>
								<div class="control-button maximize"></div>
							</div>
							<div class="terminal-title">alex.castro:~$ contact</div>
							<div class="terminal-status">
								<div class="status-indicator"></div>
								<span class="status-text">ONLINE</span>
							</div>
						</div>

						<!-- Terminal Body -->
						<div class="terminal-body">
							<div class="terminal-output">
								<div class="output-line">
									<span class="prompt">alex.castro:~$</span>
									<span class="command">./initialize_contact.sh</span>
								</div>
								<div class="output-line system-message">
									<span class="timestamp">[2024-12-19 15:30:42]</span>
									<span class="message">Initializing secure communication channel...</span>
								</div>
								<div class="output-line system-message">
									<span class="timestamp">[2024-12-19 15:30:43]</span>
									<span class="message">Loading contact protocols...</span>
								</div>
								<div class="output-line system-message">
									<span class="timestamp">[2024-12-19 15:30:44]</span>
									<span class="message">Encryption enabled. Connection secure.</span>
								</div>
								<div class="output-line success-message">
									<span class="timestamp">[2024-12-19 15:30:45]</span>
									<span class="message">✓ Contact system ready. Please enter your message below:</span>
								</div>
							</div>

							<!-- Contact Form -->
							<div class="terminal-form">
								<form id="contactForm" class="contact-form">
									<div class="form-group">
										<div class="input-line">
											<span class="input-prompt">email:~$</span>
											<input type="email" id="userEmail" name="email" placeholder="<EMAIL>" required>
										</div>
									</div>

									<div class="form-group">
										<div class="input-line">
											<span class="input-prompt">subject:~$</span>
											<input type="text" id="userSubject" name="subject" placeholder="Enter subject line" required>
										</div>
									</div>

									<div class="form-group">
										<div class="input-line message-input">
											<span class="input-prompt">message:~$</span>
											<textarea id="userMessage" name="message" placeholder="Type your message here..." required></textarea>
										</div>
									</div>

									<div class="form-actions">
										<button type="submit" class="terminal-button">
											<span class="button-text">./send_message.sh</span>
											<span class="button-icon">⚡</span>
										</button>
										<button type="button" class="terminal-button secondary" id="clearForm">
											<span class="button-text">./clear_buffer.sh</span>
											<span class="button-icon">🗑️</span>
										</button>
									</div>
								</form>
							</div>

							<!-- Terminal Footer -->
							<div class="terminal-footer">
								<div class="footer-info">
									<span class="connection-info">
										<i class="fas fa-shield-alt"></i>
										Secure connection established
									</span>
									<span class="response-time">
										<i class="fas fa-clock"></i>
										Response time: &lt; 24h
									</span>
								</div>
								<div class="typing-indicator">
									<span class="cursor">█</span>
								</div>
							</div>
						</div>
					</div>

					<!-- Terminal Background Effects -->
					<div class="terminal-background">
						<div class="scan-lines"></div>
						<div class="screen-flicker"></div>
					</div>
				</div>

				<!-- Success/Error Messages -->
				<div class="message-status" id="messageStatus">
					<div class="status-content">
						<div class="status-icon"></div>
						<div class="status-text"></div>
					</div>
				</div>
			</div>
		</section>

		<!-- EmailJS -->
		<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@emailjs/browser@4/dist/email.min.js"></script>

		<!-- Modern Preloader -->
	<script src="js/preloader-modern.js?v=1.2"></script>

	<!-- Custom JavaScript -->
		<script src="js/modern-animations.js?v=1.2"></script>
	</body>
	</html>
